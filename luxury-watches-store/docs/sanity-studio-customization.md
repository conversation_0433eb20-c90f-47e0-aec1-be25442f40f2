# Sanity Studio Customization Guide

## Making Sanity Studio WordPress-Like for Non-Technical Editors

### 1. Custom Studio Layout

#### Simplified Navigation Structure
The custom structure organizes content into intuitive sections:

- **Products** - All product-related content
- **Catalog** - Brands, collections, categories
- **Content** - Pages, blog posts, lookbooks
- **Media Library** - Organized image management
- **Customer Engagement** - Reviews and testimonials
- **Analytics** - Popular content and activity
- **Localization** - Multi-language content management

#### Visual Hierarchy
```typescript
// sanity/structure.ts - Custom icons and grouping
S.listItem()
  .title('Products')
  .icon(ShoppingBagIcon)
  .child(
    S.list()
      .title('Product Management')
      .items([
        // Organized sub-items with clear labels
      ])
  )
```

### 2. Enhanced Document Editing Experience

#### Grouped Fields for Better Organization
```typescript
// Example: Product schema with groups
groups: [
  { name: 'content', title: 'Content', default: true },
  { name: 'media', title: 'Media' },
  { name: 'pricing', title: 'Pricing & Inventory' },
  { name: 'specifications', title: 'Specifications' },
  { name: 'seo', title: 'SEO & Marketing' },
  { name: 'localization', title: 'Localization' }
]
```

#### Smart Field Descriptions and Help Text
```typescript
defineField({
  name: 'shortDescription',
  title: 'Short Description',
  type: 'text',
  description: 'Brief description for product cards and listings (max 160 characters)',
  validation: Rule => Rule.max(160)
})
```

### 3. Custom Input Components

#### Rich Text Editor for Product Descriptions
```typescript
// Custom rich text configuration
{
  type: 'array',
  of: [
    {
      type: 'block',
      styles: [
        { title: 'Normal', value: 'normal' },
        { title: 'Heading 3', value: 'h3' },
        { title: 'Heading 4', value: 'h4' },
        { title: 'Quote', value: 'blockquote' }
      ],
      marks: {
        decorators: [
          { title: 'Bold', value: 'strong' },
          { title: 'Italic', value: 'em' }
        ],
        annotations: [
          {
            title: 'Link',
            name: 'link',
            type: 'object',
            fields: [
              { name: 'href', type: 'url', title: 'URL' }
            ]
          }
        ]
      }
    }
  ]
}
```

#### Custom Price Input with Currency Support
```typescript
// sanity/components/PriceInput.tsx
import React from 'react'
import { NumberInput, Stack, Text } from '@sanity/ui'

export function PriceInput(props) {
  return (
    <Stack space={2}>
      <Text size={1} weight="medium">Price (USD)</Text>
      <NumberInput
        {...props}
        prefix="$"
        step={0.01}
        placeholder="0.00"
      />
    </Stack>
  )
}
```

#### Image Upload with Automatic Alt Text
```typescript
// Enhanced image field
{
  type: 'image',
  options: { hotspot: true },
  fields: [
    {
      name: 'alt',
      type: 'string',
      title: 'Alternative text',
      description: 'Important for SEO and accessibility',
      validation: Rule => Rule.required()
    },
    {
      name: 'caption',
      type: 'string',
      title: 'Caption'
    }
  ]
}
```

### 4. Document Previews and List Views

#### Rich Product Previews
```typescript
preview: {
  select: {
    title: 'name',
    brand: 'brand.name',
    price: 'pricing.basePrice',
    media: 'images.0',
    stock: 'inventory.stock'
  },
  prepare(selection) {
    const { title, brand, price, stock } = selection
    const stockStatus = stock > 0 ? '✓' : '⚠️'
    return {
      title,
      subtitle: `${brand} - $${price?.toLocaleString()} ${stockStatus}`,
      media: selection.media
    }
  }
}
```

#### Status Indicators
- ✓ Published and in stock
- ⚠️ Out of stock
- ○ Draft/unpublished
- 🌍 Needs translation

### 5. Workflow Enhancements

#### Document Actions Customization
```typescript
// sanity.config.ts
document: {
  actions: (prev, context) => {
    if (context.schemaType === 'product') {
      return [
        ...prev,
        // Custom action for duplicating products
        {
          name: 'duplicate-product',
          title: 'Duplicate Product',
          action: 'duplicate'
        }
      ]
    }
    return prev
  }
}
```

#### Validation Rules for Data Quality
```typescript
validation: Rule => [
  Rule.required().error('Product name is required'),
  Rule.min(3).warning('Product name should be at least 3 characters'),
  Rule.max(100).error('Product name cannot exceed 100 characters')
]
```

### 6. Dashboard Widgets

#### Quick Stats Widget
```typescript
// sanity/components/StatsWidget.tsx
export function StatsWidget() {
  return (
    <Card padding={4}>
      <Stack space={3}>
        <Text size={2} weight="bold">Quick Stats</Text>
        <Flex gap={4}>
          <Box>
            <Text size={3} weight="bold">247</Text>
            <Text size={1}>Products</Text>
          </Box>
          <Box>
            <Text size={3} weight="bold">15</Text>
            <Text size={1}>Brands</Text>
          </Box>
          <Box>
            <Text size={3} weight="bold">8</Text>
            <Text size={1}>Collections</Text>
          </Box>
        </Flex>
      </Stack>
    </Card>
  )
}
```

### 7. Localization Support

#### Language Switcher
```typescript
// Custom language field
{
  name: 'language',
  title: 'Language',
  type: 'string',
  options: {
    list: [
      { title: '🇺🇸 English', value: 'en' },
      { title: '🇩🇪 German', value: 'de' },
      { title: '🇫🇷 French', value: 'fr' },
      { title: '🇯🇵 Japanese', value: 'ja' },
      { title: '🇨🇳 Chinese', value: 'zh' },
      { title: '🇷🇺 Russian', value: 'ru' }
    ]
  },
  initialValue: 'en'
}
```

#### Translation Status Indicators
```typescript
// Show translation completeness
prepare(selection) {
  const { title, translations } = selection
  const translationCount = translations?.length || 0
  const totalLanguages = 6
  const completeness = `${translationCount}/${totalLanguages}`
  
  return {
    title,
    subtitle: `Translations: ${completeness}`
  }
}
```

### 8. Media Management

#### Organized Asset Library
- Automatic tagging by content type
- Bulk upload with metadata
- Image optimization suggestions
- Usage tracking across documents

#### Smart Image Cropping
```typescript
// Predefined crop ratios for different uses
{
  type: 'image',
  options: {
    hotspot: true,
    crops: [
      { name: 'square', title: 'Square (1:1)' },
      { name: 'landscape', title: 'Landscape (16:9)' },
      { name: 'portrait', title: 'Portrait (4:5)' }
    ]
  }
}
```

### 9. User Experience Improvements

#### Contextual Help
- Field-level tooltips
- Inline documentation
- Video tutorials for complex features
- Best practice suggestions

#### Keyboard Shortcuts
- Cmd/Ctrl + S: Save document
- Cmd/Ctrl + P: Publish document
- Cmd/Ctrl + D: Duplicate document
- Cmd/Ctrl + /: Search

#### Auto-save and Draft Management
- Automatic draft saving every 30 seconds
- Visual indicators for unsaved changes
- Conflict resolution for concurrent edits

### 10. Performance Optimizations

#### Lazy Loading
- Load document lists progressively
- Defer heavy media previews
- Optimize query performance

#### Caching Strategy
- Cache frequently accessed documents
- Preload related content
- Optimize image delivery

This customization approach transforms Sanity Studio into an intuitive, WordPress-like experience while maintaining the flexibility and power that makes Sanity ideal for luxury e-commerce content management.
