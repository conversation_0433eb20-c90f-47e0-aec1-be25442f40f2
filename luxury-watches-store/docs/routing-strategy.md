# Atlas Luxury E-commerce Routing Strategy

## URL Structure Overview

### Main Site (English Default)
- **Base URL**: `https://atlasluxury.com/`
- **Default Language**: English (no prefix)
- **Localized URLs**: `/{locale}/` for other languages

### URL Patterns

#### 1. Product URLs
```
/catalog/{product-slug}
/catalog/{brand-slug}/{product-slug}
/catalog/{category-slug}/{product-slug}
/{locale}/catalog/{product-slug}
```

#### 2. Brand URLs
```
/brands/{brand-slug}
/brands/{brand-slug}/collections
/brands/{brand-slug}/heritage
/{locale}/brands/{brand-slug}
```

#### 3. Collection URLs
```
/collections/{collection-slug}
/collections/{collection-slug}/products
/brands/{brand-slug}/collections/{collection-slug}
/{locale}/collections/{collection-slug}
```

#### 4. Category URLs
```
/catalog/{category-slug}
/catalog/{category-slug}/brands
/catalog/{category-slug}/collections
/{locale}/catalog/{category-slug}
```

#### 5. Content URLs
```
/blog/{post-slug}
/lookbooks/{lookbook-slug}
/stories/{story-slug}
/campaigns/{campaign-slug}
/{locale}/blog/{post-slug}
```

#### 6. Landing Pages
```
/landing/{page-slug}
/campaigns/{campaign-slug}
/events/{event-slug}
/{locale}/landing/{page-slug}
```

## Next.js App Router Structure

### Directory Structure
```
src/app/
├── (main)/                     # Main site layout group
│   ├── page.tsx               # Homepage
│   ├── catalog/
│   │   ├── page.tsx           # Catalog listing
│   │   ├── [slug]/
│   │   │   └── page.tsx       # Product detail
│   │   └── [category]/
│   │       ├── page.tsx       # Category listing
│   │       └── [slug]/
│   │           └── page.tsx   # Product in category
│   ├── brands/
│   │   ├── page.tsx           # Brands listing
│   │   └── [slug]/
│   │       ├── page.tsx       # Brand detail
│   │       ├── collections/
│   │       │   └── page.tsx   # Brand collections
│   │       └── heritage/
│   │           └── page.tsx   # Brand heritage
│   ├── collections/
│   │   ├── page.tsx           # Collections listing
│   │   └── [slug]/
│   │       ├── page.tsx       # Collection detail
│   │       └── products/
│   │           └── page.tsx   # Collection products
│   ├── blog/
│   │   ├── page.tsx           # Blog listing
│   │   └── [slug]/
│   │       └── page.tsx       # Blog post
│   ├── lookbooks/
│   │   ├── page.tsx           # Lookbooks listing
│   │   └── [slug]/
│   │       └── page.tsx       # Lookbook detail
│   └── landing/
│       └── [slug]/
│           └── page.tsx       # Landing pages
├── [locale]/                   # Localized routes
│   ├── catalog/
│   ├── brands/
│   ├── collections/
│   ├── blog/
│   └── lookbooks/
└── studio/                     # Sanity Studio
    └── [[...tool]]/
        └── page.tsx
```

## SEO-Optimized URL Generation

### Slug Generation Rules
1. **Product Slugs**: `{brand-name}-{product-name}-{model}`
   - Example: `rolex-submariner-date-126610ln`

2. **Brand Slugs**: `{brand-name}`
   - Example: `rolex`, `patek-philippe`, `cartier`

3. **Collection Slugs**: `{collection-name}`
   - Example: `submariner`, `nautilus`, `santos`

4. **Category Slugs**: `{category-name}`
   - Example: `luxury-watches`, `fine-jewelry`, `accessories`

### URL Canonicalization
- Primary URL: `/catalog/{product-slug}`
- Alternate URLs redirect to primary:
  - `/catalog/{brand-slug}/{product-slug}` → `/catalog/{product-slug}`
  - `/catalog/{category-slug}/{product-slug}` → `/catalog/{product-slug}`

## Internationalization (i18n) Strategy

### Supported Locales
- `en` (English) - Default, no prefix
- `de` (German) - `/de/`
- `fr` (French) - `/fr/`
- `ja` (Japanese) - `/ja/`
- `zh` (Chinese Simplified) - `/zh/`
- `ru` (Russian) - `/ru/`

### Language Detection
1. URL path locale
2. `Accept-Language` header
3. Stored user preference
4. Default to English

### Locale-Specific Content
- Product names and descriptions
- Brand stories and heritage
- Blog posts and lookbooks
- Navigation and UI text
- Currency and pricing

## Dynamic Route Handlers

### Product Routes
```typescript
// app/catalog/[slug]/page.tsx
export async function generateStaticParams() {
  const products = await getProducts()
  return products.map((product) => ({
    slug: product.slug.current
  }))
}

export async function generateMetadata({ params }) {
  const product = await getProduct(params.slug)
  return {
    title: `${product.name} | ${product.brand.name} | Atlas Luxury`,
    description: product.shortDescription,
    openGraph: {
      images: [product.images[0].url]
    }
  }
}
```

### Brand Routes
```typescript
// app/brands/[slug]/page.tsx
export async function generateStaticParams() {
  const brands = await getBrands()
  return brands.map((brand) => ({
    slug: brand.slug.current
  }))
}
```

## Sitemap Generation

### Dynamic Sitemap Structure
```typescript
// app/sitemap.ts
export default async function sitemap() {
  const baseUrl = 'https://atlasluxury.com'
  
  const [products, brands, collections, blogPosts, lookbooks] = await Promise.all([
    getProducts(),
    getBrands(),
    getCollections(),
    getBlogPosts(),
    getLookbooks()
  ])

  const routes = [
    // Static pages
    { url: baseUrl, priority: 1.0 },
    { url: `${baseUrl}/catalog`, priority: 0.9 },
    { url: `${baseUrl}/brands`, priority: 0.8 },
    
    // Dynamic product pages
    ...products.map(product => ({
      url: `${baseUrl}/catalog/${product.slug.current}`,
      lastModified: product._updatedAt,
      priority: 0.8
    })),
    
    // Dynamic brand pages
    ...brands.map(brand => ({
      url: `${baseUrl}/brands/${brand.slug.current}`,
      lastModified: brand._updatedAt,
      priority: 0.7
    }))
  ]

  return routes
}
```

## Performance Optimization

### Static Generation
- Pre-generate all product, brand, and collection pages
- Use ISR (Incremental Static Regeneration) for content updates
- Cache duration: 60 seconds for product pages, 3600 seconds for brand pages

### Image Optimization
- Use Next.js Image component with Sanity image URLs
- Implement responsive images with multiple breakpoints
- Lazy loading for below-the-fold images

### Caching Strategy
- Static pages: Cache at CDN level
- Dynamic content: Use SWR for client-side caching
- API routes: Implement Redis caching for Sanity queries

## Redirects and URL Management

### Legacy URL Redirects
```typescript
// next.config.js
module.exports = {
  async redirects() {
    return [
      {
        source: '/products/:slug',
        destination: '/catalog/:slug',
        permanent: true
      },
      {
        source: '/category/:category/:slug',
        destination: '/catalog/:slug',
        permanent: true
      }
    ]
  }
}
```

### 404 Handling
- Custom 404 page with product suggestions
- Search functionality for mistyped URLs
- Breadcrumb navigation for context

This routing strategy ensures optimal SEO performance, user experience, and maintainability for the luxury e-commerce platform.
