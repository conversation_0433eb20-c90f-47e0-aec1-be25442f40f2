import { defineField, defineType } from 'sanity'

export const productVariant = defineType({
  name: 'productVariant',
  title: 'Product Variant',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Variant Title',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'product',
      title: 'Parent Product',
      type: 'reference',
      to: [{ type: 'product' }],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'sku',
      title: 'SKU',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'attributes',
      title: 'Variant Attributes',
      type: 'object',
      fields: [
        {
          name: 'size',
          title: 'Size',
          type: 'string',
          description: 'e.g., 40mm, 42mm for watches; Ring size for jewelry'
        },
        {
          name: 'material',
          title: 'Material',
          type: 'string',
          description: 'e.g., Steel, Gold, Platinum'
        },
        {
          name: 'color',
          title: 'Color',
          type: 'string'
        },
        {
          name: 'strapType',
          title: 'Strap/Band Type',
          type: 'string',
          description: 'For watches: Leather, Metal, Rubber, etc.'
        },
        {
          name: 'gemstone',
          title: 'Gemstone',
          type: 'string',
          description: 'For jewelry: Diamond, Ruby, Sapphire, etc.'
        },
        {
          name: 'caratWeight',
          title: 'Carat Weight',
          type: 'number',
          description: 'For jewelry with gemstones'
        }
      ]
    }),
    defineField({
      name: 'pricing',
      title: 'Variant Pricing',
      type: 'object',
      fields: [
        {
          name: 'basePrice',
          title: 'Base Price (USD)',
          type: 'number',
          validation: Rule => Rule.required().positive()
        },
        {
          name: 'compareAtPrice',
          title: 'Compare at Price',
          type: 'number'
        },
        {
          name: 'costPrice',
          title: 'Cost Price',
          type: 'number',
          description: 'Internal cost for margin calculations'
        }
      ]
    }),
    defineField({
      name: 'inventory',
      title: 'Inventory',
      type: 'object',
      fields: [
        {
          name: 'quantity',
          title: 'Quantity',
          type: 'number',
          validation: Rule => Rule.required().min(0)
        },
        {
          name: 'reserved',
          title: 'Reserved Quantity',
          type: 'number',
          initialValue: 0
        },
        {
          name: 'location',
          title: 'Storage Location',
          type: 'string'
        }
      ]
    }),
    defineField({
      name: 'images',
      title: 'Variant Images',
      type: 'array',
      of: [
        {
          type: 'image',
          options: { hotspot: true },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative text'
            }
          ]
        }
      ]
    }),
    defineField({
      name: 'isAvailable',
      title: 'Available',
      type: 'boolean',
      initialValue: true
    }),
    defineField({
      name: 'weight',
      title: 'Weight (grams)',
      type: 'number'
    }),
    defineField({
      name: 'dimensions',
      title: 'Dimensions',
      type: 'object',
      fields: [
        { name: 'length', title: 'Length (mm)', type: 'number' },
        { name: 'width', title: 'Width (mm)', type: 'number' },
        { name: 'height', title: 'Height (mm)', type: 'number' }
      ]
    })
  ],
  preview: {
    select: {
      title: 'title',
      product: 'product.name',
      price: 'pricing.basePrice',
      media: 'images.0'
    },
    prepare(selection) {
      const { title, product, price } = selection
      return {
        title: `${product} - ${title}`,
        subtitle: price ? `$${price.toLocaleString()}` : 'No price set'
      }
    }
  }
})
