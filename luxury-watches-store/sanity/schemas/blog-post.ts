import { defineField, defineType } from 'sanity'
import { BookOpenIcon } from '@heroicons/react/24/outline'

export const blogPost = defineType({
  name: 'blogPost',
  title: 'Blog Post',
  type: 'document',
  icon: BookOpenIcon,
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'media',
      title: 'Media'
    },
    {
      name: 'settings',
      title: 'Settings & SEO'
    }
  ],
  fields: [
    defineField({
      name: 'title',
      title: 'Article Title',
      type: 'string',
      group: 'content',
      description: 'The main title of the blog post',
      validation: Rule => [
        Rule.required().error('Article title is required'),
        Rule.min(10).warning('Title should be at least 10 characters'),
        Rule.max(100).error('Title cannot exceed 100 characters')
      ]
    }),
    defineField({
      name: 'slug',
      title: 'URL Slug',
      type: 'slug',
      group: 'content',
      description: 'Auto-generated from title. Used in URLs.',
      options: {
        source: 'title',
        maxLength: 96,
        slugify: input => input
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]+/g, '')
          .slice(0, 96)
      },
      validation: Rule => Rule.required().error('URL slug is required')
    }),
    defineField({
      name: 'excerpt',
      title: 'Article Excerpt',
      type: 'text',
      group: 'content',
      rows: 3,
      description: 'Brief summary for article previews and social sharing (max 200 characters)',
      validation: Rule => Rule.max(200).warning('Keep excerpt under 200 characters for best display')
    }),
    defineField({
      name: 'featuredImage',
      title: 'Featured Image',
      type: 'image',
      options: {
        hotspot: true,
      }
    }),
    defineField({
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            { title: 'Normal', value: 'normal' },
            { title: 'H1', value: 'h1' },
            { title: 'H2', value: 'h2' },
            { title: 'H3', value: 'h3' },
            { title: 'Quote', value: 'blockquote' },
          ],
          marks: {
            decorators: [
              { title: 'Strong', value: 'strong' },
              { title: 'Emphasis', value: 'em' },
            ],
            annotations: [
              {
                title: 'URL',
                name: 'link',
                type: 'object',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url',
                  },
                ],
              },
            ],
          },
        },
        {
          type: 'image',
          options: { hotspot: true },
        },
      ]
    }),
    defineField({
      name: 'author',
      title: 'Author Information',
      type: 'object',
      group: 'content',
      fields: [
        {
          name: 'name',
          title: 'Author Name',
          type: 'string',
          validation: Rule => Rule.required()
        },
        {
          name: 'bio',
          title: 'Author Bio',
          type: 'text',
          rows: 2,
          description: 'Brief author biography'
        },
        {
          name: 'avatar',
          title: 'Author Avatar',
          type: 'image',
          options: { hotspot: true },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative Text',
              validation: Rule => Rule.required()
            }
          ]
        }
      ]
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published Date',
      type: 'datetime',
      group: 'settings',
      description: 'When the article was published'
    }),
    defineField({
      name: 'categories',
      title: 'Article Categories',
      type: 'array',
      group: 'settings',
      of: [{ type: 'string' }],
      description: 'Select relevant categories for this article',
      options: {
        list: [
          { title: '⌚ Watch Reviews', value: 'reviews' },
          { title: '🏢 Brand Stories', value: 'brands' },
          { title: '📰 Industry News', value: 'news' },
          { title: '📖 Buying Guides', value: 'guides' },
          { title: '🔧 Maintenance Tips', value: 'maintenance' },
          { title: '💎 Jewelry Insights', value: 'jewelry' },
          { title: '🎯 Investment Advice', value: 'investment' },
          { title: '🎨 Design & Craftsmanship', value: 'design' }
        ]
      },
      validation: Rule => Rule.min(1).max(3).error('Select 1-3 categories')
    }),
    defineField({
      name: 'tags',
      title: 'Article Tags',
      type: 'array',
      group: 'settings',
      of: [{ type: 'string' }],
      description: 'Tags for better searchability and organization',
      options: {
        layout: 'tags'
      }
    }),
    defineField({
      name: 'relatedProducts',
      title: 'Related Products',
      type: 'array',
      group: 'content',
      of: [{ type: 'reference', to: [{ type: 'product' }] }],
      description: 'Products mentioned or featured in this article',
      validation: Rule => Rule.max(5).warning('Maximum 5 related products recommended')
    }),
    defineField({
      name: 'isPublished',
      title: 'Published Status',
      type: 'boolean',
      group: 'settings',
      description: 'Show/hide article from public view',
      initialValue: false
    }),
    defineField({
      name: 'isFeatured',
      title: 'Featured Article',
      type: 'boolean',
      group: 'settings',
      description: 'Display in featured sections',
      initialValue: false
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      group: 'settings',
      description: 'Search engine optimization settings',
      fields: [
        {
          name: 'title',
          title: 'SEO Title',
          type: 'string',
          description: 'Custom title for search engines (leave empty to use article title)',
          validation: Rule => Rule.max(60).warning('SEO titles should be under 60 characters')
        },
        {
          name: 'description',
          title: 'SEO Description',
          type: 'text',
          rows: 3,
          description: 'Description for search engine results',
          validation: Rule => Rule.max(160).warning('SEO descriptions should be under 160 characters')
        },
        {
          name: 'keywords',
          title: 'SEO Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'Keywords for search optimization',
          options: { layout: 'tags' }
        },
        {
          name: 'noIndex',
          title: 'Hide from Search Engines',
          type: 'boolean',
          description: 'Prevent search engines from indexing this article',
          initialValue: false
        }
      ]
    }),
    defineField({
      name: 'language',
      title: 'Language',
      type: 'string',
      group: 'settings',
      options: {
        list: [
          { title: '🇺🇸 English', value: 'en' },
          { title: '🇩🇪 German', value: 'de' },
          { title: '🇫🇷 French', value: 'fr' },
          { title: '🇯🇵 Japanese', value: 'ja' },
          { title: '🇨🇳 Chinese', value: 'zh' },
          { title: '🇷🇺 Russian', value: 'ru' }
        ]
      },
      initialValue: 'en',
      validation: Rule => Rule.required()
    })
  ],
  preview: {
    select: {
      title: 'title',
      author: 'author.name',
      media: 'featuredImage',
      isPublished: 'isPublished',
      isFeatured: 'isFeatured',
      publishedAt: 'publishedAt'
    },
    prepare(selection) {
      const { title, author, isPublished, isFeatured, publishedAt } = selection
      const statusIcon = isPublished ? '✓' : '○'
      const featuredIcon = isFeatured ? '⭐' : ''
      const dateDisplay = publishedAt ? new Date(publishedAt).toLocaleDateString() : 'Draft'

      return {
        title: `${title} ${featuredIcon}`,
        subtitle: `${author ? `by ${author}` : 'No author'} • ${dateDisplay} ${statusIcon}`,
        media: selection.media
      }
    }
  },
  orderings: [
    {
      title: 'Published Date (Newest)',
      name: 'publishedDesc',
      by: [{ field: 'publishedAt', direction: 'desc' }]
    },
    {
      title: 'Title A-Z',
      name: 'titleAsc',
      by: [{ field: 'title', direction: 'asc' }]
    },
    {
      title: 'Recently Updated',
      name: 'updatedDesc',
      by: [{ field: '_updatedAt', direction: 'desc' }]
    }
  ]
})
