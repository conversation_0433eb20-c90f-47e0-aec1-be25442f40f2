import { defineField, defineType } from 'sanity'

export const lookbook = defineType({
  name: 'lookbook',
  title: 'Lookbook',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Lookbook Title',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'subtitle',
      title: 'Subtitle',
      type: 'string'
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 3
    }),
    defineField({
      name: 'coverImage',
      title: 'Cover Image',
      type: 'image',
      options: { hotspot: true },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'season',
      title: 'Season/Collection',
      type: 'string',
      options: {
        list: [
          { title: 'Spring/Summer', value: 'spring-summer' },
          { title: 'Fall/Winter', value: 'fall-winter' },
          { title: 'Holiday', value: 'holiday' },
          { title: 'Timeless', value: 'timeless' }
        ]
      }
    }),
    defineField({
      name: 'year',
      title: 'Year',
      type: 'number',
      validation: Rule => Rule.required().min(2020).max(2030)
    }),
    defineField({
      name: 'brand',
      title: 'Featured Brand',
      type: 'reference',
      to: [{ type: 'brand' }]
    }),
    defineField({
      name: 'collection',
      title: 'Featured Collection',
      type: 'reference',
      to: [{ type: 'collection' }]
    }),
    defineField({
      name: 'looks',
      title: 'Looks',
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'look',
          title: 'Look',
          fields: [
            {
              name: 'title',
              title: 'Look Title',
              type: 'string',
              validation: Rule => Rule.required()
            },
            {
              name: 'description',
              title: 'Look Description',
              type: 'text',
              rows: 2
            },
            {
              name: 'mainImage',
              title: 'Main Image',
              type: 'image',
              options: { hotspot: true },
              validation: Rule => Rule.required()
            },
            {
              name: 'additionalImages',
              title: 'Additional Images',
              type: 'array',
              of: [{ type: 'image', options: { hotspot: true } }]
            },
            {
              name: 'products',
              title: 'Featured Products',
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    {
                      name: 'product',
                      title: 'Product',
                      type: 'reference',
                      to: [{ type: 'product' }]
                    },
                    {
                      name: 'position',
                      title: 'Position in Image',
                      type: 'object',
                      fields: [
                        { name: 'x', title: 'X Position (%)', type: 'number', validation: Rule => Rule.min(0).max(100) },
                        { name: 'y', title: 'Y Position (%)', type: 'number', validation: Rule => Rule.min(0).max(100) }
                      ]
                    },
                    {
                      name: 'note',
                      title: 'Product Note',
                      type: 'string'
                    }
                  ]
                }
              ]
            },
            {
              name: 'styling',
              title: 'Styling Notes',
              type: 'object',
              fields: [
                { name: 'mood', title: 'Mood', type: 'string' },
                { name: 'occasion', title: 'Occasion', type: 'string' },
                { name: 'inspiration', title: 'Inspiration', type: 'text', rows: 2 }
              ]
            }
          ],
          preview: {
            select: {
              title: 'title',
              media: 'mainImage'
            }
          }
        }
      ]
    }),
    defineField({
      name: 'photographer',
      title: 'Photographer',
      type: 'object',
      fields: [
        { name: 'name', title: 'Name', type: 'string' },
        { name: 'website', title: 'Website', type: 'url' },
        { name: 'instagram', title: 'Instagram', type: 'string' }
      ]
    }),
    defineField({
      name: 'stylist',
      title: 'Stylist',
      type: 'object',
      fields: [
        { name: 'name', title: 'Name', type: 'string' },
        { name: 'website', title: 'Website', type: 'url' },
        { name: 'instagram', title: 'Instagram', type: 'string' }
      ]
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags'
      }
    }),
    defineField({
      name: 'isPublished',
      title: 'Published',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime'
    }),
    defineField({
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        { name: 'title', title: 'SEO Title', type: 'string' },
        { name: 'description', title: 'SEO Description', type: 'text', rows: 3 },
        { name: 'keywords', title: 'Keywords', type: 'array', of: [{ type: 'string' }] }
      ]
    })
  ],
  preview: {
    select: {
      title: 'title',
      season: 'season',
      year: 'year',
      media: 'coverImage',
      isPublished: 'isPublished'
    },
    prepare(selection) {
      const { title, season, year, isPublished } = selection
      return {
        title,
        subtitle: `${season} ${year} ${isPublished ? '✓' : '○'}`
      }
    }
  }
})
