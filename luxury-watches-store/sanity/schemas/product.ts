import { defineField, defineType } from 'sanity'
import { ShoppingBagIcon } from '@heroicons/react/24/outline'

export const product = defineType({
  name: 'product',
  title: 'Product',
  type: 'document',
  icon: ShoppingBagIcon,
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true,
      icon: ShoppingBagIcon
    },
    {
      name: 'media',
      title: 'Media & Gallery'
    },
    {
      name: 'pricing',
      title: 'Pricing & Inventory'
    },
    {
      name: 'specifications',
      title: 'Specifications'
    },
    {
      name: 'seo',
      title: 'SEO & Marketing'
    },
    {
      name: 'localization',
      title: 'Localization'
    }
  ],
  fieldsets: [
    {
      name: 'basicInfo',
      title: 'Basic Information',
      options: { collapsible: false }
    },
    {
      name: 'categorization',
      title: 'Categorization',
      options: { collapsible: true, collapsed: false }
    },
    {
      name: 'visibility',
      title: 'Visibility & Status',
      options: { collapsible: true, collapsed: true }
    }
  ],
  fields: [
    // Content Group - Basic Information
    defineField({
      name: 'name',
      title: 'Product Name',
      type: 'string',
      group: 'content',
      fieldset: 'basicInfo',
      description: 'The display name of the product (e.g., "Submariner Date")',
      validation: Rule => [
        Rule.required().error('Product name is required'),
        Rule.min(3).warning('Product name should be at least 3 characters'),
        Rule.max(100).error('Product name cannot exceed 100 characters')
      ]
    }),
    defineField({
      name: 'slug',
      title: 'URL Slug',
      type: 'slug',
      group: 'content',
      fieldset: 'basicInfo',
      description: 'Auto-generated from product name. Used in URLs.',
      options: {
        source: 'name',
        maxLength: 96,
        slugify: input => input
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]+/g, '')
          .slice(0, 96)
      },
      validation: Rule => Rule.required().error('URL slug is required for SEO')
    }),
    defineField({
      name: 'productType',
      title: 'Product Type',
      type: 'string',
      group: 'content',
      fieldset: 'categorization',
      description: 'Primary product category',
      options: {
        list: [
          { title: '⌚ Watch', value: 'watch' },
          { title: '💎 Jewelry', value: 'jewelry' },
          { title: '👜 Accessory', value: 'accessory' }
        ],
        layout: 'radio'
      },
      validation: Rule => Rule.required().error('Product type is required')
    }),
    defineField({
      name: 'brand',
      title: 'Brand',
      type: 'reference',
      group: 'content',
      fieldset: 'categorization',
      to: [{ type: 'brand' }],
      description: 'Select the brand (e.g., Rolex, Patek Philippe)',
      validation: Rule => Rule.required().error('Brand is required'),
      options: {
        filter: 'isActive == true'
      }
    }),
    defineField({
      name: 'collection',
      title: 'Collection',
      type: 'reference',
      group: 'content',
      fieldset: 'categorization',
      to: [{ type: 'collection' }],
      description: 'Optional: Select a specific collection within the brand',
      options: {
        filter: 'brand._ref == $brandId && isActive == true'
      }
    }),
    defineField({
      name: 'category',
      title: 'Category',
      type: 'reference',
      group: 'content',
      fieldset: 'categorization',
      to: [{ type: 'category' }],
      description: 'Primary category for navigation and filtering',
      validation: Rule => Rule.required().error('Category is required'),
      options: {
        filter: 'isActive == true'
      }
    }),
    defineField({
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      group: 'content',
      rows: 2,
      description: 'Brief description for product cards and listings (max 160 characters)',
      validation: Rule => Rule.max(160).warning('Keep under 160 characters for best display')
    }),
    defineField({
      name: 'description',
      title: 'Full Description',
      type: 'array',
      group: 'content',
      of: [
        {
          type: 'block',
          styles: [
            { title: 'Normal', value: 'normal' },
            { title: 'H3', value: 'h3' },
            { title: 'H4', value: 'h4' },
            { title: 'Quote', value: 'blockquote' }
          ],
          marks: {
            decorators: [
              { title: 'Strong', value: 'strong' },
              { title: 'Emphasis', value: 'em' }
            ]
          }
        }
      ]
    }),

    // Media Group
    defineField({
      name: 'images',
      title: 'Product Images',
      type: 'array',
      group: 'media',
      description: 'Upload 1-10 high-quality product images. First image will be used as primary.',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true
          },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative Text',
              description: 'Describe the image for accessibility and SEO',
              validation: Rule => Rule.required().min(10).max(100)
            },
            {
              name: 'caption',
              type: 'string',
              title: 'Caption',
              description: 'Optional caption displayed with the image'
            },
            {
              name: 'isHero',
              type: 'boolean',
              title: 'Hero Image',
              description: 'Mark as main product image for listings',
              initialValue: false
            },
            {
              name: 'sortOrder',
              type: 'number',
              title: 'Sort Order',
              description: 'Order of appearance (lower numbers first)',
              initialValue: 0
            }
          ]
        }
      ],
      validation: Rule => [
        Rule.required().error('At least one product image is required'),
        Rule.min(1).error('Minimum 1 image required'),
        Rule.max(10).warning('Maximum 10 images recommended for performance')
      ]
    }),
    defineField({
      name: 'gallery',
      title: 'Additional Gallery',
      type: 'array',
      group: 'media',
      of: [
        {
          type: 'image',
          options: { hotspot: true }
        },
        {
          type: 'file',
          options: {
            accept: 'video/*'
          }
        }
      ]
    }),
    // Pricing & Inventory Group
    defineField({
      name: 'pricing',
      title: 'Pricing Information',
      type: 'object',
      group: 'pricing',
      description: 'Set pricing for different markets and currencies',
      fieldsets: [
        {
          name: 'basePricing',
          title: 'Base Pricing (USD)',
          options: { collapsible: false }
        },
        {
          name: 'regionalPricing',
          title: 'Regional Pricing',
          options: { collapsible: true, collapsed: true }
        }
      ],
      fields: [
        {
          name: 'basePrice',
          title: 'Base Price (USD)',
          type: 'number',
          fieldset: 'basePricing',
          description: 'Primary price in US Dollars',
          components: {
            input: (props: any) => {
              const { PriceInput } = require('../components/custom-inputs')
              return PriceInput(props)
            }
          },
          validation: Rule => Rule.required().positive().error('Base price must be a positive number')
        },
        {
          name: 'originalPrice',
          title: 'Original Price (USD)',
          type: 'number',
          fieldset: 'basePricing',
          description: 'Original price before discount (optional)',
          validation: Rule => Rule.positive().custom((originalPrice, context) => {
            const basePrice = (context.parent as any)?.basePrice
            if (originalPrice && basePrice && originalPrice <= basePrice) {
              return 'Original price should be higher than base price'
            }
            return true
          })
        },
        {
          name: 'priceOnRequest',
          title: 'Price on Request',
          type: 'boolean',
          fieldset: 'basePricing',
          description: 'Hide price and show "Price on Request" for exclusive items',
          initialValue: false
        },
        {
          name: 'regionalPricing',
          title: 'Regional Pricing',
          type: 'array',
          fieldset: 'regionalPricing',
          description: 'Set prices for different currencies and regions',
          of: [
            {
              type: 'object',
              title: 'Regional Price',
              fields: [
                {
                  name: 'currency',
                  title: 'Currency',
                  type: 'string',
                  options: {
                    list: [
                      { title: '🇺🇸 USD', value: 'USD' },
                      { title: '🇪🇺 EUR', value: 'EUR' },
                      { title: '🇬🇧 GBP', value: 'GBP' },
                      { title: '🇯🇵 JPY', value: 'JPY' },
                      { title: '🇨🇳 CNY', value: 'CNY' },
                      { title: '🇷🇺 RUB', value: 'RUB' }
                    ]
                  },
                  validation: Rule => Rule.required()
                },
                {
                  name: 'price',
                  title: 'Price',
                  type: 'number',
                  validation: Rule => Rule.required().positive()
                },
                {
                  name: 'originalPrice',
                  title: 'Original Price',
                  type: 'number',
                  validation: Rule => Rule.positive()
                }
              ],
              preview: {
                select: {
                  currency: 'currency',
                  price: 'price'
                },
                prepare({ currency, price }) {
                  return {
                    title: `${currency}: ${price?.toLocaleString()}`
                  }
                }
              }
            }
          ]
        }
      ]
    }),
    defineField({
      name: 'inventory',
      title: 'Inventory Management',
      type: 'object',
      group: 'pricing',
      description: 'Manage stock levels and availability',
      fields: [
        {
          name: 'stock',
          title: 'Stock Quantity',
          type: 'number',
          description: 'Current available quantity',
          components: {
            input: (props: any) => {
              const { StockInput } = require('../components/custom-inputs')
              return StockInput(props)
            }
          },
          validation: Rule => Rule.required().min(0).error('Stock must be 0 or positive'),
          initialValue: 0
        },
        {
          name: 'lowStockThreshold',
          title: 'Low Stock Alert',
          type: 'number',
          description: 'Alert when stock falls below this number',
          validation: Rule => Rule.min(0),
          initialValue: 5
        },
        {
          name: 'trackInventory',
          title: 'Track Inventory',
          type: 'boolean',
          description: 'Enable inventory tracking for this product',
          initialValue: true
        },
        {
          name: 'allowBackorder',
          title: 'Allow Backorder',
          type: 'boolean',
          description: 'Allow purchases when out of stock',
          initialValue: false
        },
        {
          name: 'sku',
          title: 'SKU',
          type: 'string',
          description: 'Stock Keeping Unit - unique product identifier',
          validation: Rule => Rule.required().regex(/^[A-Z0-9-]+$/, {
            name: 'SKU format',
            invert: false
          }).error('SKU must contain only uppercase letters, numbers, and hyphens')
        }
      ]
    }),
    defineField({
      name: 'specifications',
      title: 'Product Specifications',
      type: 'object',
      group: 'specifications',
      description: 'Technical specifications and features',
      fieldsets: [
        {
          name: 'watchSpecs',
          title: 'Watch Specifications',
          options: { collapsible: true, collapsed: false }
        },
        {
          name: 'jewelrySpecs',
          title: 'Jewelry Specifications',
          options: { collapsible: true, collapsed: true }
        }
      ],
      fields: [
        {
          name: 'movement',
          title: 'Movement',
          type: 'string',
          fieldset: 'watchSpecs',
          description: 'e.g., Automatic, Quartz, Manual'
        },
        {
          name: 'caseMaterial',
          title: 'Case Material',
          type: 'string',
          fieldset: 'watchSpecs',
          description: 'e.g., Stainless Steel, Gold, Titanium'
        },
        {
          name: 'caseSize',
          title: 'Case Size',
          type: 'string',
          fieldset: 'watchSpecs',
          description: 'e.g., 40mm, 42mm'
        },
        {
          name: 'dialColor',
          title: 'Dial Color',
          type: 'string',
          fieldset: 'watchSpecs'
        },
        {
          name: 'strapMaterial',
          title: 'Strap/Bracelet Material',
          type: 'string',
          fieldset: 'watchSpecs',
          description: 'e.g., Leather, Steel, Rubber'
        },
        {
          name: 'waterResistance',
          title: 'Water Resistance',
          type: 'string',
          fieldset: 'watchSpecs',
          description: 'e.g., 100m, 300m, 30 ATM'
        },
        {
          name: 'functions',
          title: 'Functions & Complications',
          type: 'array',
          fieldset: 'watchSpecs',
          of: [{ type: 'string' }],
          description: 'e.g., Date, GMT, Chronograph'
        },
        {
          name: 'metalType',
          title: 'Metal Type',
          type: 'string',
          fieldset: 'jewelrySpecs',
          description: 'e.g., 18K Gold, Platinum, Silver'
        },
        {
          name: 'gemstones',
          title: 'Gemstones',
          type: 'array',
          fieldset: 'jewelrySpecs',
          of: [{ type: 'string' }],
          description: 'e.g., Diamond, Ruby, Sapphire'
        },
        {
          name: 'caratWeight',
          title: 'Total Carat Weight',
          type: 'number',
          fieldset: 'jewelrySpecs',
          description: 'Total weight of gemstones in carats'
        }
      ]
    }),

    // Status and Visibility Fields
    defineField({
      name: 'isAvailable',
      title: 'Available for Purchase',
      type: 'boolean',
      group: 'content',
      fieldset: 'visibility',
      description: 'Show/hide product from store',
      initialValue: true
    }),
    defineField({
      name: 'isFeatured',
      title: 'Featured Product',
      type: 'boolean',
      group: 'content',
      fieldset: 'visibility',
      description: 'Display in featured sections',
      initialValue: false
    }),
    defineField({
      name: 'isNew',
      title: 'New Product',
      type: 'boolean',
      group: 'content',
      fieldset: 'visibility',
      description: 'Mark as new arrival',
      initialValue: false
    }),
    defineField({
      name: 'tags',
      title: 'Product Tags',
      type: 'array',
      group: 'content',
      of: [{ type: 'string' }],
      description: 'Tags for filtering and search (e.g., luxury, vintage, limited)',
      options: {
        layout: 'tags'
      }
    }),
    defineField({
      name: 'seo',
      title: 'SEO & Marketing',
      type: 'object',
      group: 'seo',
      description: 'Search engine optimization and marketing settings',
      fields: [
        {
          name: 'title',
          title: 'SEO Title',
          type: 'string',
          description: 'Custom title for search engines (leave empty to use product name)',
          validation: Rule => Rule.max(60).warning('SEO titles should be under 60 characters')
        },
        {
          name: 'description',
          title: 'SEO Description',
          type: 'text',
          rows: 3,
          description: 'Description for search engine results',
          validation: Rule => Rule.max(160).warning('SEO descriptions should be under 160 characters')
        },
        {
          name: 'keywords',
          title: 'SEO Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'Keywords for search optimization',
          options: { layout: 'tags' }
        },
        {
          name: 'noIndex',
          title: 'Hide from Search Engines',
          type: 'boolean',
          description: 'Prevent search engines from indexing this product',
          initialValue: false
        }
      ]
    }),

    // Localization placeholder (will be enhanced in task 5)
    defineField({
      name: 'language',
      title: 'Language',
      type: 'string',
      group: 'localization',
      options: {
        list: [
          { title: '🇺🇸 English', value: 'en' },
          { title: '🇩🇪 German', value: 'de' },
          { title: '🇫🇷 French', value: 'fr' },
          { title: '🇯🇵 Japanese', value: 'ja' },
          { title: '🇨🇳 Chinese', value: 'zh' },
          { title: '🇷🇺 Russian', value: 'ru' }
        ]
      },
      initialValue: 'en',
      validation: Rule => Rule.required()
    })
  ],
  preview: {
    select: {
      title: 'name',
      brand: 'brand.name',
      media: 'images.0',
      basePrice: 'pricing.basePrice',
      stock: 'inventory.stock',
      isAvailable: 'isAvailable',
      isFeatured: 'isFeatured'
    },
    prepare(selection) {
      const { title, brand, basePrice, stock, isAvailable, isFeatured } = selection

      // Status indicators
      let statusIcon = '○' // Draft
      if (isAvailable && stock > 0) statusIcon = '✓' // Available
      else if (isAvailable && stock === 0) statusIcon = '⚠️' // Out of stock
      else if (!isAvailable) statusIcon = '○' // Unavailable

      const featuredIcon = isFeatured ? '⭐' : ''
      const priceDisplay = basePrice ? `$${basePrice.toLocaleString()}` : 'Price TBD'

      return {
        title: `${title} ${featuredIcon}`,
        subtitle: `${brand} • ${priceDisplay} ${statusIcon}`,
        media: selection.media
      }
    }
  },
  orderings: [
    {
      title: 'Name A-Z',
      name: 'nameAsc',
      by: [{ field: 'name', direction: 'asc' }]
    },
    {
      title: 'Price Low-High',
      name: 'priceAsc',
      by: [{ field: 'pricing.basePrice', direction: 'asc' }]
    },
    {
      title: 'Recently Added',
      name: 'createdDesc',
      by: [{ field: '_createdAt', direction: 'desc' }]
    },
    {
      title: 'Recently Updated',
      name: 'updatedDesc',
      by: [{ field: '_updatedAt', direction: 'desc' }]
    }
  ]
})
