import { defineField, defineType } from 'sanity'

export const product = defineType({
  name: 'product',
  title: 'Product',
  type: 'document',
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'media',
      title: 'Media'
    },
    {
      name: 'pricing',
      title: 'Pricing & Inventory'
    },
    {
      name: 'specifications',
      title: 'Specifications'
    },
    {
      name: 'seo',
      title: 'SEO & Marketing'
    },
    {
      name: 'localization',
      title: 'Localization'
    }
  ],
  fields: [
    // Content Group
    defineField({
      name: 'name',
      title: 'Product Name',
      type: 'string',
      group: 'content',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      group: 'content',
      options: {
        source: 'name',
        maxLength: 96,
        slugify: input => input
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]+/g, '')
          .slice(0, 96)
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'productType',
      title: 'Product Type',
      type: 'string',
      group: 'content',
      options: {
        list: [
          { title: 'Watch', value: 'watch' },
          { title: 'Jewelry', value: 'jewelry' },
          { title: 'Accessory', value: 'accessory' }
        ]
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'brand',
      title: 'Brand',
      type: 'reference',
      group: 'content',
      to: [{ type: 'brand' }],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'collection',
      title: 'Collection',
      type: 'reference',
      group: 'content',
      to: [{ type: 'collection' }]
    }),
    defineField({
      name: 'category',
      title: 'Category',
      type: 'reference',
      group: 'content',
      to: [{ type: 'category' }],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      group: 'content',
      rows: 2,
      description: 'Brief description for product cards and listings'
    }),
    defineField({
      name: 'description',
      title: 'Full Description',
      type: 'array',
      group: 'content',
      of: [
        {
          type: 'block',
          styles: [
            { title: 'Normal', value: 'normal' },
            { title: 'H3', value: 'h3' },
            { title: 'H4', value: 'h4' },
            { title: 'Quote', value: 'blockquote' }
          ],
          marks: {
            decorators: [
              { title: 'Strong', value: 'strong' },
              { title: 'Emphasis', value: 'em' }
            ]
          }
        }
      ]
    }),

    // Media Group
    defineField({
      name: 'images',
      title: 'Product Images',
      type: 'array',
      group: 'media',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative text',
              validation: Rule => Rule.required()
            },
            {
              name: 'caption',
              type: 'string',
              title: 'Caption'
            },
            {
              name: 'isHero',
              type: 'boolean',
              title: 'Hero Image',
              description: 'Mark as main product image'
            }
          ]
        }
      ],
      validation: Rule => Rule.required().min(1).max(10)
    }),
    defineField({
      name: 'gallery',
      title: 'Additional Gallery',
      type: 'array',
      group: 'media',
      of: [
        {
          type: 'image',
          options: { hotspot: true }
        },
        {
          type: 'file',
          options: {
            accept: 'video/*'
          }
        }
      ]
    }),
    // Pricing & Inventory Group
    defineField({
      name: 'pricing',
      title: 'Pricing Information',
      type: 'object',
      group: 'pricing',
      fields: [
        {
          name: 'basePrice',
          title: 'Base Price (USD)',
          type: 'number',
          validation: Rule => Rule.required().positive()
        },
        {
          name: 'originalPrice',
          title: 'Original Price (USD)',
          type: 'number',
          description: 'Used for showing discounts'
        },
        {
          name: 'regionalPricing',
          title: 'Regional Pricing',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'currency', title: 'Currency', type: 'string', options: { list: ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'RUB'] } },
                { name: 'price', title: 'Price', type: 'number' },
                { name: 'originalPrice', title: 'Original Price', type: 'number' }
              ]
            }
          ]
        },
        {
          name: 'priceOnRequest',
          title: 'Price on Request',
          type: 'boolean',
          description: 'Hide price and show "Price on Request"'
        }
      ]
    }),
    defineField({
      name: 'inventory',
      title: 'Inventory Management',
      type: 'object',
      group: 'pricing',
      fields: [
        {
          name: 'stock',
          title: 'Stock Quantity',
          type: 'number',
          validation: Rule => Rule.required().min(0)
        },
        {
          name: 'lowStockThreshold',
          title: 'Low Stock Alert',
          type: 'number',
          initialValue: 5
        },
        {
          name: 'trackInventory',
          title: 'Track Inventory',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'allowBackorder',
          title: 'Allow Backorder',
          type: 'boolean',
          initialValue: false
        }
      ]
    }),
    defineField({
      name: 'specifications',
      title: 'Specifications',
      type: 'object',
      fields: [
        { name: 'movement', title: 'Movement', type: 'string' },
        { name: 'caseMaterial', title: 'Case Material', type: 'string' },
        { name: 'caseSize', title: 'Case Size', type: 'string' },
        { name: 'dialColor', title: 'Dial Color', type: 'string' },
        { name: 'strapMaterial', title: 'Strap Material', type: 'string' },
        { name: 'waterResistance', title: 'Water Resistance', type: 'string' },
        { name: 'functions', title: 'Functions', type: 'array', of: [{ type: 'string' }] },
      ]
    }),
    defineField({
      name: 'stock',
      title: 'Stock Quantity',
      type: 'number',
      validation: Rule => Rule.required().min(0)
    }),
    defineField({
      name: 'isAvailable',
      title: 'Available for Purchase',
      type: 'boolean',
      initialValue: true
    }),
    defineField({
      name: 'isFeatured',
      title: 'Featured Product',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags'
      }
    }),
    defineField({
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        { name: 'title', title: 'SEO Title', type: 'string' },
        { name: 'description', title: 'SEO Description', type: 'text', rows: 3 },
        { name: 'keywords', title: 'Keywords', type: 'array', of: [{ type: 'string' }] },
      ]
    }),
  ],
  preview: {
    select: {
      title: 'name',
      brand: 'brand.name',
      media: 'images.0',
      price: 'price'
    },
    prepare(selection) {
      const { title, brand, price } = selection
      return {
        title,
        subtitle: `${brand} - $${price?.toLocaleString()}`
      }
    }
  }
})
