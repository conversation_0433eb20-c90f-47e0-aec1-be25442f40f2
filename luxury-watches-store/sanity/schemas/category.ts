import { defineField, defineType } from 'sanity'
import { TagIcon } from '@heroicons/react/24/outline'

export const category = defineType({
  name: 'category',
  title: 'Category',
  type: 'document',
  icon: TagIcon,
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'display',
      title: 'Display & Navigation'
    },
    {
      name: 'seo',
      title: 'SEO'
    }
  ],
  fields: [
    defineField({
      name: 'name',
      title: 'Category Name',
      type: 'string',
      group: 'content',
      description: 'Display name for the category (e.g., "Luxury Watches")',
      validation: Rule => [
        Rule.required().error('Category name is required'),
        Rule.min(2).warning('Category name should be at least 2 characters'),
        Rule.max(50).error('Category name cannot exceed 50 characters')
      ]
    }),
    defineField({
      name: 'slug',
      title: 'URL Slug',
      type: 'slug',
      group: 'content',
      description: 'Auto-generated from category name. Used in URLs.',
      options: {
        source: 'name',
        maxLength: 96,
        slugify: input => input
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]+/g, '')
          .slice(0, 96)
      },
      validation: Rule => Rule.required().error('URL slug is required')
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      group: 'content',
      rows: 3,
      description: 'Brief description of the category',
      validation: Rule => Rule.max(200).warning('Keep description under 200 characters')
    }),
    defineField({
      name: 'parentCategory',
      title: 'Parent Category',
      type: 'reference',
      group: 'content',
      to: [{ type: 'category' }],
      description: 'Optional: Create category hierarchy',
      options: {
        filter: ({ document }) => ({
          filter: '_id != $id',
          params: { id: document._id }
        })
      }
    }),
    defineField({
      name: 'icon',
      title: 'Category Icon',
      type: 'string',
      group: 'display',
      description: 'Lucide icon name (e.g., "watch", "gem", "crown")',
      options: {
        list: [
          { title: '⌚ Watch', value: 'watch' },
          { title: '💎 Gem', value: 'gem' },
          { title: '👑 Crown', value: 'crown' },
          { title: '💍 Ring', value: 'circle' },
          { title: '📿 Necklace', value: 'link' },
          { title: '⚡ Luxury', value: 'zap' },
          { title: '🏆 Premium', value: 'trophy' },
          { title: '✨ Exclusive', value: 'sparkles' }
        ]
      }
    }),
    defineField({
      name: 'image',
      title: 'Category Image',
      type: 'image',
      group: 'display',
      description: 'Hero image for category pages',
      options: {
        hotspot: true
      },
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative Text',
          description: 'Describe the image for accessibility',
          validation: Rule => Rule.required().min(10).max(100)
        }
      ]
    }),
    defineField({
      name: 'color',
      title: 'Brand Color',
      type: 'string',
      group: 'display',
      description: 'Hex color code for category theming (e.g., #1a1a1a)',
      validation: Rule => Rule.regex(/^#[0-9A-Fa-f]{6}$/, {
        name: 'hex color',
        invert: false
      }).warning('Please enter a valid hex color code (e.g., #1a1a1a)')
    }),
    defineField({
      name: 'isActive',
      title: 'Active Category',
      type: 'boolean',
      group: 'display',
      description: 'Show/hide category in navigation and listings',
      initialValue: true
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      type: 'number',
      group: 'display',
      description: 'Lower numbers appear first in navigation',
      validation: Rule => Rule.min(0),
      initialValue: 0
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      group: 'seo',
      description: 'Search engine optimization settings',
      fields: [
        {
          name: 'title',
          title: 'SEO Title',
          type: 'string',
          description: 'Custom title for search engines (leave empty to use category name)',
          validation: Rule => Rule.max(60).warning('SEO titles should be under 60 characters')
        },
        {
          name: 'description',
          title: 'SEO Description',
          type: 'text',
          rows: 3,
          description: 'Description for search engine results',
          validation: Rule => Rule.max(160).warning('SEO descriptions should be under 160 characters')
        },
        {
          name: 'keywords',
          title: 'SEO Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'Keywords for search optimization',
          options: { layout: 'tags' }
        }
      ]
    }),
    defineField({
      name: 'language',
      title: 'Language',
      type: 'string',
      options: {
        list: [
          { title: '🇺🇸 English', value: 'en' },
          { title: '🇩🇪 German', value: 'de' },
          { title: '🇫🇷 French', value: 'fr' },
          { title: '🇯🇵 Japanese', value: 'ja' },
          { title: '🇨🇳 Chinese', value: 'zh' },
          { title: '🇷🇺 Russian', value: 'ru' }
        ]
      },
      initialValue: 'en',
      validation: Rule => Rule.required()
    })
  ],
  preview: {
    select: {
      title: 'name',
      media: 'image',
      isActive: 'isActive',
      sortOrder: 'sortOrder',
      parentCategory: 'parentCategory.name'
    },
    prepare(selection) {
      const { title, isActive, sortOrder, parentCategory } = selection
      const statusIcon = isActive ? '✓' : '○'
      const hierarchy = parentCategory ? `${parentCategory} > ` : ''

      return {
        title: `${hierarchy}${title}`,
        subtitle: `Order: ${sortOrder || 0} ${statusIcon}`,
        media: selection.media
      }
    }
  },
  orderings: [
    {
      title: 'Sort Order',
      name: 'sortOrder',
      by: [{ field: 'sortOrder', direction: 'asc' }]
    },
    {
      title: 'Name A-Z',
      name: 'nameAsc',
      by: [{ field: 'name', direction: 'asc' }]
    },
    {
      title: 'Recently Created',
      name: 'createdDesc',
      by: [{ field: '_createdAt', direction: 'desc' }]
    }
  ]
})
