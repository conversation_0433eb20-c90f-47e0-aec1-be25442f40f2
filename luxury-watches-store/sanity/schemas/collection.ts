import { defineField, defineType } from 'sanity'
import { BookOpenIcon } from '@heroicons/react/24/outline'

export const collection = defineType({
  name: 'collection',
  title: 'Collection',
  type: 'document',
  icon: BookOpenIcon,
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'media',
      title: 'Media & Visuals'
    },
    {
      name: 'settings',
      title: 'Settings & SEO'
    }
  ],
  fields: [
    defineField({
      name: 'name',
      title: 'Collection Name',
      type: 'string',
      group: 'content',
      description: 'Name of the collection (e.g., "Submariner", "Datejust")',
      validation: Rule => [
        Rule.required().error('Collection name is required'),
        Rule.min(2).warning('Collection name should be at least 2 characters'),
        Rule.max(80).error('Collection name cannot exceed 80 characters')
      ]
    }),
    defineField({
      name: 'slug',
      title: 'URL Slug',
      type: 'slug',
      group: 'content',
      description: 'Auto-generated from collection name. Used in URLs.',
      options: {
        source: 'name',
        maxLength: 96,
        slugify: input => input
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]+/g, '')
          .slice(0, 96)
      },
      validation: Rule => Rule.required().error('URL slug is required')
    }),
    defineField({
      name: 'brand',
      title: 'Brand',
      type: 'reference',
      group: 'content',
      to: [{ type: 'brand' }],
      description: 'The brand this collection belongs to',
      validation: Rule => Rule.required().error('Brand is required'),
      options: {
        filter: 'isActive == true'
      }
    }),
    defineField({
      name: 'description',
      title: 'Collection Description',
      type: 'array',
      group: 'content',
      description: 'Rich description of the collection',
      of: [
        {
          type: 'block',
          styles: [
            { title: 'Normal', value: 'normal' },
            { title: 'H3', value: 'h3' },
            { title: 'H4', value: 'h4' },
            { title: 'Quote', value: 'blockquote' }
          ],
          marks: {
            decorators: [
              { title: 'Strong', value: 'strong' },
              { title: 'Emphasis', value: 'em' }
            ]
          }
        }
      ]
    }),
    defineField({
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      group: 'content',
      rows: 2,
      description: 'Brief description for collection cards (max 160 characters)',
      validation: Rule => Rule.max(160).warning('Keep under 160 characters for best display')
    }),
    defineField({
      name: 'launchYear',
      title: 'Launch Year',
      type: 'number',
      group: 'content',
      description: 'Year the collection was first introduced',
      validation: Rule => Rule.min(1800).max(new Date().getFullYear() + 5)
    }),
    defineField({
      name: 'heritage',
      title: 'Heritage Story',
      type: 'text',
      group: 'content',
      rows: 4,
      description: 'Historical background and significance of the collection'
    }),

    // Media Group
    defineField({
      name: 'image',
      title: 'Collection Hero Image',
      type: 'image',
      group: 'media',
      description: 'Main image representing the collection',
      options: {
        hotspot: true,
        crops: [
          { name: 'hero', title: 'Hero Banner (21:9)' },
          { name: 'card', title: 'Collection Card (4:3)' },
          { name: 'square', title: 'Square (1:1)' }
        ]
      },
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative Text',
          description: 'Describe the image for accessibility',
          validation: Rule => Rule.required().min(10).max(100)
        }
      ],
      validation: Rule => Rule.required().error('Collection image is required')
    }),
    defineField({
      name: 'gallery',
      title: 'Collection Gallery',
      type: 'array',
      group: 'media',
      description: 'Additional images showcasing the collection',
      of: [
        {
          type: 'image',
          options: { hotspot: true },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative Text',
              validation: Rule => Rule.required()
            },
            {
              name: 'caption',
              type: 'string',
              title: 'Caption'
            }
          ]
        }
      ],
      validation: Rule => Rule.max(8).warning('Maximum 8 gallery images recommended')
    }),

    // Settings Group
    defineField({
      name: 'isActive',
      title: 'Active Collection',
      type: 'boolean',
      group: 'settings',
      description: 'Show/hide collection from store',
      initialValue: true
    }),
    defineField({
      name: 'isFeatured',
      title: 'Featured Collection',
      type: 'boolean',
      group: 'settings',
      description: 'Display in featured sections',
      initialValue: false
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      type: 'number',
      group: 'settings',
      description: 'Order in collection listings (lower numbers first)',
      validation: Rule => Rule.min(0),
      initialValue: 0
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      group: 'settings',
      description: 'Search engine optimization settings',
      fields: [
        {
          name: 'title',
          title: 'SEO Title',
          type: 'string',
          description: 'Custom title for search engines (leave empty to use collection name)',
          validation: Rule => Rule.max(60).warning('SEO titles should be under 60 characters')
        },
        {
          name: 'description',
          title: 'SEO Description',
          type: 'text',
          rows: 3,
          description: 'Description for search engine results',
          validation: Rule => Rule.max(160).warning('SEO descriptions should be under 160 characters')
        },
        {
          name: 'keywords',
          title: 'SEO Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'Keywords for search optimization',
          options: { layout: 'tags' }
        }
      ]
    }),
    defineField({
      name: 'language',
      title: 'Language',
      type: 'string',
      group: 'settings',
      options: {
        list: [
          { title: '🇺🇸 English', value: 'en' },
          { title: '🇩🇪 German', value: 'de' },
          { title: '🇫🇷 French', value: 'fr' },
          { title: '🇯🇵 Japanese', value: 'ja' },
          { title: '🇨🇳 Chinese', value: 'zh' },
          { title: '🇷🇺 Russian', value: 'ru' }
        ]
      },
      initialValue: 'en',
      validation: Rule => Rule.required()
    })
  ],
  preview: {
    select: {
      title: 'name',
      brand: 'brand.name',
      media: 'image',
      year: 'launchYear',
      isActive: 'isActive',
      isFeatured: 'isFeatured'
    },
    prepare(selection) {
      const { title, brand, year, isActive, isFeatured } = selection
      const statusIcon = isActive ? '✓' : '○'
      const featuredIcon = isFeatured ? '⭐' : ''
      const yearDisplay = year ? ` (${year})` : ''

      return {
        title: `${title} ${featuredIcon}`,
        subtitle: `${brand}${yearDisplay} ${statusIcon}`,
        media: selection.media
      }
    }
  },
  orderings: [
    {
      title: 'Sort Order',
      name: 'sortOrder',
      by: [{ field: 'sortOrder', direction: 'asc' }]
    },
    {
      title: 'Name A-Z',
      name: 'nameAsc',
      by: [{ field: 'name', direction: 'asc' }]
    },
    {
      title: 'Launch Year (Newest)',
      name: 'yearDesc',
      by: [{ field: 'launchYear', direction: 'desc' }]
    },
    {
      title: 'Recently Created',
      name: 'createdDesc',
      by: [{ field: '_createdAt', direction: 'desc' }]
    }
  ]
})
