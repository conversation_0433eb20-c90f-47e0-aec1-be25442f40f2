import { defineField, defineType } from 'sanity'

export const siteSettingsEnhanced = defineType({
  name: 'siteSettings',
  title: 'Site Settings',
  type: 'document',
  groups: [
    {
      name: 'general',
      title: 'General',
      default: true
    },
    {
      name: 'navigation',
      title: 'Navigation'
    },
    {
      name: 'localization',
      title: 'Localization'
    },
    {
      name: 'social',
      title: 'Social Media'
    },
    {
      name: 'seo',
      title: 'SEO'
    },
    {
      name: 'ecommerce',
      title: 'E-commerce'
    }
  ],
  fields: [
    // General Settings
    defineField({
      name: 'siteName',
      title: 'Site Name',
      type: 'string',
      group: 'general',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'siteDescription',
      title: 'Site Description',
      type: 'text',
      group: 'general',
      rows: 3
    }),
    defineField({
      name: 'logo',
      title: 'Site Logo',
      type: 'image',
      group: 'general',
      options: { hotspot: true }
    }),
    defineField({
      name: 'favicon',
      title: 'Favicon',
      type: 'image',
      group: 'general'
    }),
    defineField({
      name: 'contactInfo',
      title: 'Contact Information',
      type: 'object',
      group: 'general',
      fields: [
        { name: 'email', title: 'Email', type: 'email' },
        { name: 'phone', title: 'Phone', type: 'string' },
        { name: 'address', title: 'Address', type: 'text', rows: 3 },
        { name: 'businessHours', title: 'Business Hours', type: 'text', rows: 2 }
      ]
    }),

    // Navigation
    defineField({
      name: 'mainNavigation',
      title: 'Main Navigation',
      type: 'array',
      group: 'navigation',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'title', title: 'Title', type: 'string' },
            { name: 'url', title: 'URL', type: 'string' },
            { name: 'isExternal', title: 'External Link', type: 'boolean' },
            {
              name: 'megaMenu',
              title: 'Mega Menu',
              type: 'object',
              fields: [
                { name: 'enabled', title: 'Enable Mega Menu', type: 'boolean' },
                {
                  name: 'sections',
                  title: 'Menu Sections',
                  type: 'array',
                  of: [
                    {
                      type: 'object',
                      fields: [
                        { name: 'title', title: 'Section Title', type: 'string' },
                        {
                          name: 'links',
                          title: 'Links',
                          type: 'array',
                          of: [
                            {
                              type: 'object',
                              fields: [
                                { name: 'title', title: 'Link Title', type: 'string' },
                                { name: 'url', title: 'URL', type: 'string' },
                                { name: 'description', title: 'Description', type: 'string' }
                              ]
                            }
                          ]
                        },
                        { name: 'featuredImage', title: 'Featured Image', type: 'image' },
                        { name: 'featuredProducts', title: 'Featured Products', type: 'array', of: [{ type: 'reference', to: [{ type: 'product' }] }] }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }),
    defineField({
      name: 'footerNavigation',
      title: 'Footer Navigation',
      type: 'object',
      group: 'navigation',
      fields: [
        {
          name: 'sections',
          title: 'Footer Sections',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'title', title: 'Section Title', type: 'string' },
                {
                  name: 'links',
                  title: 'Links',
                  type: 'array',
                  of: [
                    {
                      type: 'object',
                      fields: [
                        { name: 'title', title: 'Link Title', type: 'string' },
                        { name: 'url', title: 'URL', type: 'string' }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        },
        { name: 'copyrightText', title: 'Copyright Text', type: 'string' },
        { name: 'newsletter', title: 'Newsletter Signup', type: 'boolean' }
      ]
    }),

    // Localization
    defineField({
      name: 'localization',
      title: 'Localization Settings',
      type: 'object',
      group: 'localization',
      fields: [
        {
          name: 'defaultLanguage',
          title: 'Default Language',
          type: 'string',
          options: {
            list: [
              { title: 'English', value: 'en' },
              { title: 'German', value: 'de' },
              { title: 'French', value: 'fr' },
              { title: 'Japanese', value: 'ja' },
              { title: 'Chinese (Simplified)', value: 'zh' },
              { title: 'Russian', value: 'ru' }
            ]
          },
          initialValue: 'en'
        },
        {
          name: 'supportedLanguages',
          title: 'Supported Languages',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            list: [
              { title: 'English', value: 'en' },
              { title: 'German', value: 'de' },
              { title: 'French', value: 'fr' },
              { title: 'Japanese', value: 'ja' },
              { title: 'Chinese (Simplified)', value: 'zh' },
              { title: 'Russian', value: 'ru' }
            ]
          }
        },
        {
          name: 'currencies',
          title: 'Supported Currencies',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'code', title: 'Currency Code', type: 'string' },
                { name: 'symbol', title: 'Symbol', type: 'string' },
                { name: 'name', title: 'Name', type: 'string' },
                { name: 'exchangeRate', title: 'Exchange Rate (vs USD)', type: 'number' }
              ]
            }
          ]
        }
      ]
    }),

    // Social Media
    defineField({
      name: 'socialMedia',
      title: 'Social Media',
      type: 'object',
      group: 'social',
      fields: [
        { name: 'instagram', title: 'Instagram', type: 'url' },
        { name: 'facebook', title: 'Facebook', type: 'url' },
        { name: 'twitter', title: 'Twitter/X', type: 'url' },
        { name: 'youtube', title: 'YouTube', type: 'url' },
        { name: 'linkedin', title: 'LinkedIn', type: 'url' },
        { name: 'pinterest', title: 'Pinterest', type: 'url' },
        { name: 'tiktok', title: 'TikTok', type: 'url' }
      ]
    }),

    // SEO
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      group: 'seo',
      fields: [
        { name: 'defaultTitle', title: 'Default Page Title', type: 'string' },
        { name: 'titleTemplate', title: 'Title Template', type: 'string', description: 'Use %s for page title placeholder' },
        { name: 'defaultDescription', title: 'Default Meta Description', type: 'text', rows: 3 },
        { name: 'keywords', title: 'Default Keywords', type: 'array', of: [{ type: 'string' }] },
        { name: 'ogImage', title: 'Default Social Share Image', type: 'image' },
        { name: 'twitterHandle', title: 'Twitter Handle', type: 'string' },
        { name: 'googleAnalyticsId', title: 'Google Analytics ID', type: 'string' },
        { name: 'googleTagManagerId', title: 'Google Tag Manager ID', type: 'string' }
      ]
    }),

    // E-commerce
    defineField({
      name: 'ecommerce',
      title: 'E-commerce Settings',
      type: 'object',
      group: 'ecommerce',
      fields: [
        { name: 'defaultCurrency', title: 'Default Currency', type: 'string', initialValue: 'USD' },
        { name: 'taxRate', title: 'Default Tax Rate (%)', type: 'number' },
        { name: 'freeShippingThreshold', title: 'Free Shipping Threshold', type: 'number' },
        { name: 'returnPolicy', title: 'Return Policy (days)', type: 'number' },
        { name: 'warrantyPeriod', title: 'Warranty Period (months)', type: 'number' },
        {
          name: 'paymentMethods',
          title: 'Accepted Payment Methods',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            list: [
              { title: 'Credit Card', value: 'credit-card' },
              { title: 'PayPal', value: 'paypal' },
              { title: 'Apple Pay', value: 'apple-pay' },
              { title: 'Google Pay', value: 'google-pay' },
              { title: 'Bank Transfer', value: 'bank-transfer' },
              { title: 'Cryptocurrency', value: 'crypto' }
            ]
          }
        }
      ]
    })
  ],
  preview: {
    select: {
      title: 'siteName'
    },
    prepare(selection) {
      return {
        title: selection.title || 'Site Settings'
      }
    }
  }
})
