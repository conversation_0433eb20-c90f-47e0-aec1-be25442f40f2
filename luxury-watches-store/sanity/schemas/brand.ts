import { defineField, defineType } from 'sanity'

export const brand = defineType({
  name: 'brand',
  title: 'Brand',
  type: 'document',
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'media',
      title: 'Media & Assets'
    },
    {
      name: 'localization',
      title: 'Localization'
    },
    {
      name: 'seo',
      title: 'SEO'
    }
  ],
  fields: [
    defineField({
      name: 'name',
      title: 'Brand Name',
      type: 'string',
      group: 'content',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      group: 'content',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'tagline',
      title: 'Brand Tagline',
      type: 'string',
      group: 'content',
      description: 'Short memorable phrase representing the brand'
    }),
    defineField({
      name: 'description',
      title: 'Brand Description',
      type: 'array',
      group: 'content',
      of: [{ type: 'block' }]
    }),
    defineField({
      name: 'founded',
      title: 'Founded Year',
      type: 'number',
      group: 'content'
    }),
    define<PERSON>ield({
      name: 'founder',
      title: 'Founder(s)',
      type: 'string',
      group: 'content'
    }),
    defineField({
      name: 'country',
      title: 'Country of Origin',
      type: 'string',
      group: 'content'
    }),
    defineField({
      name: 'headquarters',
      title: 'Headquarters',
      type: 'string',
      group: 'content'
    }),
    defineField({
      name: 'website',
      title: 'Official Website',
      type: 'url',
      group: 'content'
    }),
    defineField({
      name: 'heritage',
      title: 'Brand Heritage',
      type: 'array',
      group: 'content',
      of: [{ type: 'block' }]
    }),
    defineField({
      name: 'brandValues',
      title: 'Brand Values',
      type: 'array',
      group: 'content',
      of: [{ type: 'string' }],
      description: 'Core values that define the brand'
    }),
    defineField({
      name: 'isLuxury',
      title: 'Luxury Brand',
      type: 'boolean',
      group: 'content',
      initialValue: true
    }),
    defineField({
      name: 'luxuryTier',
      title: 'Luxury Tier',
      type: 'string',
      group: 'content',
      options: {
        list: [
          { title: 'Ultra-Luxury', value: 'ultra-luxury' },
          { title: 'High-End Luxury', value: 'high-end' },
          { title: 'Accessible Luxury', value: 'accessible' },
          { title: 'Premium', value: 'premium' }
        ]
      }
    }),
    defineField({
      name: 'priceRange',
      title: 'Price Range (USD)',
      type: 'object',
      group: 'content',
      fields: [
        { name: 'min', title: 'Minimum Price', type: 'number' },
        { name: 'max', title: 'Maximum Price', type: 'number' },
        { name: 'averagePrice', title: 'Average Price', type: 'number' }
      ]
    }),

    // Media & Assets Group
    defineField({
      name: 'logo',
      title: 'Brand Logo',
      type: 'image',
      group: 'media',
      options: { hotspot: true },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'logoVariants',
      title: 'Logo Variants',
      type: 'object',
      group: 'media',
      fields: [
        { name: 'light', title: 'Light Version', type: 'image', options: { hotspot: true } },
        { name: 'dark', title: 'Dark Version', type: 'image', options: { hotspot: true } },
        { name: 'monogram', title: 'Monogram', type: 'image', options: { hotspot: true } }
      ]
    }),
    defineField({
      name: 'brandImages',
      title: 'Brand Images',
      type: 'object',
      group: 'media',
      fields: [
        { name: 'hero', title: 'Hero Image', type: 'image', options: { hotspot: true } },
        { name: 'gallery', title: 'Brand Gallery', type: 'array', of: [{ type: 'image', options: { hotspot: true } }] },
        { name: 'lifestyle', title: 'Lifestyle Images', type: 'array', of: [{ type: 'image', options: { hotspot: true } }] }
      ]
    }),
    defineField({
      name: 'brandColors',
      title: 'Brand Colors',
      type: 'object',
      group: 'media',
      fields: [
        { name: 'primary', title: 'Primary Color', type: 'string' },
        { name: 'secondary', title: 'Secondary Color', type: 'string' },
        { name: 'accent', title: 'Accent Color', type: 'string' }
      ]
    }),
    defineField({
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        { name: 'title', title: 'SEO Title', type: 'string' },
        { name: 'description', title: 'SEO Description', type: 'text', rows: 3 },
      ]
    }),
  ],
  preview: {
    select: {
      title: 'name',
      media: 'logo',
      country: 'country',
      founded: 'founded'
    },
    prepare(selection) {
      const { title, country, founded } = selection
      return {
        title,
        subtitle: `${country} ${founded ? `(${founded})` : ''}`
      }
    }
  }
})
