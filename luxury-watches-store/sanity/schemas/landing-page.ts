import { defineField, defineType } from 'sanity'

export const landingPage = defineType({
  name: 'landingPage',
  title: 'Landing Page',
  type: 'document',
  groups: [
    {
      name: 'content',
      title: 'Content',
      default: true
    },
    {
      name: 'hero',
      title: 'Hero Section'
    },
    {
      name: 'sections',
      title: 'Page Sections'
    },
    {
      name: 'seo',
      title: 'SEO'
    }
  ],
  fields: [
    defineField({
      name: 'title',
      title: 'Page Title',
      type: 'string',
      group: 'content',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      group: 'content',
      options: {
        source: 'title',
        maxLength: 96
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'pageType',
      title: 'Page Type',
      type: 'string',
      group: 'content',
      options: {
        list: [
          { title: 'Campaign Landing', value: 'campaign' },
          { title: 'Brand Story', value: 'brand-story' },
          { title: 'Collection Showcase', value: 'collection' },
          { title: 'Seasonal Campaign', value: 'seasonal' },
          { title: 'Event Page', value: 'event' },
          { title: 'Lookbook', value: 'lookbook' }
        ]
      },
      validation: Rule => Rule.required()
    }),

    // Hero Section
    defineField({
      name: 'hero',
      title: 'Hero Section',
      type: 'object',
      group: 'hero',
      fields: [
        {
          name: 'headline',
          title: 'Headline',
          type: 'string',
          validation: Rule => Rule.required()
        },
        {
          name: 'subheadline',
          title: 'Subheadline',
          type: 'text',
          rows: 2
        },
        {
          name: 'backgroundMedia',
          title: 'Background Media',
          type: 'object',
          fields: [
            {
              name: 'type',
              title: 'Media Type',
              type: 'string',
              options: {
                list: [
                  { title: 'Image', value: 'image' },
                  { title: 'Video', value: 'video' }
                ]
              }
            },
            {
              name: 'image',
              title: 'Background Image',
              type: 'image',
              options: { hotspot: true },
              hidden: ({ parent }) => parent?.type !== 'image'
            },
            {
              name: 'video',
              title: 'Background Video',
              type: 'file',
              options: { accept: 'video/*' },
              hidden: ({ parent }) => parent?.type !== 'video'
            }
          ]
        },
        {
          name: 'ctaButtons',
          title: 'Call-to-Action Buttons',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'text', title: 'Button Text', type: 'string' },
                { name: 'url', title: 'URL', type: 'url' },
                { name: 'style', title: 'Style', type: 'string', options: { list: ['primary', 'secondary', 'outline'] } }
              ]
            }
          ],
          validation: Rule => Rule.max(2)
        },
        {
          name: 'overlay',
          title: 'Overlay Settings',
          type: 'object',
          fields: [
            { name: 'opacity', title: 'Overlay Opacity', type: 'number', validation: Rule => Rule.min(0).max(1) },
            { name: 'color', title: 'Overlay Color', type: 'string' }
          ]
        }
      ]
    }),

    // Page Sections
    defineField({
      name: 'sections',
      title: 'Page Sections',
      type: 'array',
      group: 'sections',
      of: [
        {
          name: 'textSection',
          title: 'Text Section',
          type: 'object',
          fields: [
            { name: 'title', title: 'Section Title', type: 'string' },
            { name: 'content', title: 'Content', type: 'array', of: [{ type: 'block' }] },
            { name: 'alignment', title: 'Text Alignment', type: 'string', options: { list: ['left', 'center', 'right'] } }
          ]
        },
        {
          name: 'imageGallery',
          title: 'Image Gallery',
          type: 'object',
          fields: [
            { name: 'title', title: 'Gallery Title', type: 'string' },
            { name: 'images', title: 'Images', type: 'array', of: [{ type: 'image', options: { hotspot: true } }] },
            { name: 'layout', title: 'Layout', type: 'string', options: { list: ['grid', 'masonry', 'carousel'] } }
          ]
        },
        {
          name: 'productShowcase',
          title: 'Product Showcase',
          type: 'object',
          fields: [
            { name: 'title', title: 'Section Title', type: 'string' },
            { name: 'products', title: 'Featured Products', type: 'array', of: [{ type: 'reference', to: [{ type: 'product' }] }] },
            { name: 'layout', title: 'Layout', type: 'string', options: { list: ['grid', 'carousel', 'featured'] } }
          ]
        },
        {
          name: 'testimonials',
          title: 'Testimonials',
          type: 'object',
          fields: [
            { name: 'title', title: 'Section Title', type: 'string' },
            {
              name: 'testimonials',
              title: 'Testimonials',
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    { name: 'quote', title: 'Quote', type: 'text' },
                    { name: 'author', title: 'Author', type: 'string' },
                    { name: 'title', title: 'Author Title', type: 'string' },
                    { name: 'image', title: 'Author Image', type: 'image' }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }),

    // SEO
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      group: 'seo',
      fields: [
        { name: 'title', title: 'SEO Title', type: 'string' },
        { name: 'description', title: 'Meta Description', type: 'text', rows: 3 },
        { name: 'keywords', title: 'Keywords', type: 'array', of: [{ type: 'string' }] },
        { name: 'ogImage', title: 'Social Share Image', type: 'image' }
      ]
    }),

    defineField({
      name: 'isPublished',
      title: 'Published',
      type: 'boolean',
      group: 'content',
      initialValue: false
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      group: 'content'
    })
  ],
  preview: {
    select: {
      title: 'title',
      pageType: 'pageType',
      slug: 'slug.current',
      isPublished: 'isPublished'
    },
    prepare(selection) {
      const { title, pageType, slug, isPublished } = selection
      return {
        title,
        subtitle: `${pageType} • /${slug} ${isPublished ? '✓' : '○'}`
      }
    }
  }
})
