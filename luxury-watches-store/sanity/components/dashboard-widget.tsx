import React, { useState, useEffect } from 'react'
import { Card, Text, Flex, Grid, <PERSON><PERSON>, Badge, Button } from '@sanity/ui'
import { 
  TrendUpIcon, 
  TrendDownIcon, 
  EyeOpenIcon, 
  EditIcon,
  PublishIcon,
  UnpublishIcon
} from '@sanity/icons'
import { useClient } from 'sanity'

interface DashboardStats {
  products: {
    total: number
    published: number
    drafts: number
    featured: number
    outOfStock: number
  }
  content: {
    blogPosts: number
    landingPages: number
    collections: number
    brands: number
  }
  recentActivity: Array<{
    _id: string
    _type: string
    _updatedAt: string
    name?: string
    title?: string
    isPublished?: boolean
    isAvailable?: boolean
  }>
}

export function DashboardWidget() {
  const client = useClient()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      
      const [
        productStats,
        contentStats,
        recentActivity
      ] = await Promise.all([
        // Product statistics
        client.fetch(`{
          "total": count(*[_type == "product"]),
          "published": count(*[_type == "product" && isAvailable == true]),
          "drafts": count(*[_type == "product" && isAvailable != true]),
          "featured": count(*[_type == "product" && isFeatured == true]),
          "outOfStock": count(*[_type == "product" && inventory.stock <= 0])
        }`),
        
        // Content statistics
        client.fetch(`{
          "blogPosts": count(*[_type == "blogPost"]),
          "landingPages": count(*[_type == "landingPage"]),
          "collections": count(*[_type == "collection"]),
          "brands": count(*[_type == "brand"])
        }`),
        
        // Recent activity
        client.fetch(`*[_type in ["product", "blogPost", "landingPage", "collection"]] 
          | order(_updatedAt desc) [0...8] {
          _id,
          _type,
          _updatedAt,
          name,
          title,
          isPublished,
          isAvailable
        }`)
      ])

      setStats({
        products: productStats,
        content: contentStats,
        recentActivity
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card padding={4}>
        <Stack space={4}>
          <Text size={3} weight="bold">📊 Dashboard Overview</Text>
          <Text>Loading statistics...</Text>
        </Stack>
      </Card>
    )
  }

  if (!stats) {
    return (
      <Card padding={4} tone="critical">
        <Text>Failed to load dashboard statistics</Text>
      </Card>
    )
  }

  return (
    <Stack space={4}>
      {/* Welcome Header */}
      <Card padding={4} tone="primary">
        <Stack space={3}>
          <Flex align="center" gap={3}>
            <Text size={4} weight="bold">
              ⚜️ Welcome to Atlas Luxury CMS
            </Text>
          </Flex>
          <Text size={2}>
            Manage your luxury timepieces and jewelry collection with ease
          </Text>
        </Stack>
      </Card>

      {/* Quick Stats */}
      <Grid columns={[2, 4]} gap={3}>
        <StatCard
          title="Total Products"
          value={stats.products.total}
          icon="📦"
          color="primary"
        />
        <StatCard
          title="Published"
          value={stats.products.published}
          icon="✅"
          color="positive"
          subtitle={`${Math.round((stats.products.published / stats.products.total) * 100)}% live`}
        />
        <StatCard
          title="Drafts"
          value={stats.products.drafts}
          icon="📝"
          color="caution"
        />
        <StatCard
          title="Featured"
          value={stats.products.featured}
          icon="⭐"
          color="primary"
        />
      </Grid>

      {/* Content Overview */}
      <Card padding={4}>
        <Stack space={3}>
          <Text size={3} weight="bold">📚 Content Overview</Text>
          <Grid columns={[2, 4]} gap={3}>
            <ContentMetric
              label="Blog Posts"
              value={stats.content.blogPosts}
              icon="📝"
              link="/desk/blogPost"
            />
            <ContentMetric
              label="Landing Pages"
              value={stats.content.landingPages}
              icon="🎯"
              link="/desk/landingPage"
            />
            <ContentMetric
              label="Collections"
              value={stats.content.collections}
              icon="📚"
              link="/desk/collection"
            />
            <ContentMetric
              label="Brands"
              value={stats.content.brands}
              icon="🏢"
              link="/desk/brand"
            />
          </Grid>
        </Stack>
      </Card>

      {/* Recent Activity */}
      <Card padding={4}>
        <Stack space={3}>
          <Flex align="center" justify="space-between">
            <Text size={3} weight="bold">🕒 Recent Activity</Text>
            <Button
              text="Refresh"
              mode="ghost"
              onClick={fetchDashboardStats}
            />
          </Flex>
          
          <Stack space={2}>
            {stats.recentActivity.map((item) => (
              <ActivityItem key={item._id} item={item} />
            ))}
          </Stack>
        </Stack>
      </Card>

      {/* Quick Actions */}
      <Card padding={4}>
        <Stack space={3}>
          <Text size={3} weight="bold">⚡ Quick Actions</Text>
          <Grid columns={[2, 3]} gap={3}>
            <QuickActionButton
              title="Add New Product"
              description="Create a new luxury timepiece"
              icon="➕"
              href="/desk/product;new"
            />
            <QuickActionButton
              title="Write Blog Post"
              description="Share luxury insights"
              icon="✍️"
              href="/desk/blogPost;new"
            />
            <QuickActionButton
              title="Create Landing Page"
              description="Build conversion pages"
              icon="🎯"
              href="/desk/landingPage;new"
            />
          </Grid>
        </Stack>
      </Card>
    </Stack>
  )
}

function StatCard({ 
  title, 
  value, 
  icon, 
  color = 'default', 
  subtitle 
}: {
  title: string
  value: number
  icon: string
  color?: string
  subtitle?: string
}) {
  return (
    <Card padding={3} tone={color as any}>
      <Stack space={2}>
        <Flex align="center" gap={2}>
          <span style={{ fontSize: '20px' }}>{icon}</span>
          <Text size={1} weight="medium">
            {title}
          </Text>
        </Flex>
        <Text size={3} weight="bold">
          {value.toLocaleString()}
        </Text>
        {subtitle && (
          <Text size={0} muted>
            {subtitle}
          </Text>
        )}
      </Stack>
    </Card>
  )
}

function ContentMetric({ 
  label, 
  value, 
  icon, 
  link 
}: {
  label: string
  value: number
  icon: string
  link: string
}) {
  return (
    <Card padding={3} tone="transparent" style={{ cursor: 'pointer' }}>
      <Stack space={2}>
        <Flex align="center" gap={2}>
          <span style={{ fontSize: '16px' }}>{icon}</span>
          <Text size={1} weight="medium">
            {label}
          </Text>
        </Flex>
        <Text size={2} weight="bold">
          {value}
        </Text>
      </Stack>
    </Card>
  )
}

function ActivityItem({ item }: { item: any }) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product': return '📦'
      case 'blogPost': return '📝'
      case 'landingPage': return '🎯'
      case 'collection': return '📚'
      case 'brand': return '🏢'
      default: return '📄'
    }
  }

  const getStatusBadge = (item: any) => {
    if (item._type === 'product') {
      return item.isAvailable ? 
        <Badge tone="positive" text="Live" /> : 
        <Badge tone="default" text="Draft" />
    }
    if (item._type === 'blogPost' || item._type === 'landingPage') {
      return item.isPublished ? 
        <Badge tone="positive" text="Published" /> : 
        <Badge tone="default" text="Draft" />
    }
    return <Badge tone="primary" text="Active" />
  }

  return (
    <Card padding={3} tone="transparent" border>
      <Flex align="center" justify="space-between">
        <Flex align="center" gap={3}>
          <span style={{ fontSize: '16px' }}>{getTypeIcon(item._type)}</span>
          <div>
            <Text size={1} weight="medium">
              {item.name || item.title || 'Untitled'}
            </Text>
            <Text size={0} muted>
              {new Date(item._updatedAt).toLocaleDateString()} • {item._type}
            </Text>
          </div>
        </Flex>
        {getStatusBadge(item)}
      </Flex>
    </Card>
  )
}

function QuickActionButton({ 
  title, 
  description, 
  icon, 
  href 
}: {
  title: string
  description: string
  icon: string
  href: string
}) {
  return (
    <Card padding={3} tone="transparent" border style={{ cursor: 'pointer' }}>
      <Stack space={2}>
        <Flex align="center" gap={2}>
          <span style={{ fontSize: '20px' }}>{icon}</span>
          <Text size={2} weight="bold">
            {title}
          </Text>
        </Flex>
        <Text size={1} muted>
          {description}
        </Text>
      </Stack>
    </Card>
  )
}
