import React, { useState, useCallback } from 'react'
import { Card, Text, <PERSON>lex, Stack, Button, Badge, Grid } from '@sanity/ui'
import { PatchEvent, set, unset, insert } from 'sanity'
import { ImageIcon, TrashIcon, UploadIcon, EyeOpenIcon } from '@sanity/icons'

interface ImageInputProps {
  value?: any[]
  onChange: (event: PatchEvent) => void
  schemaType: any
}

export function ImageInput({ value = [], onChange, schemaType }: ImageInputProps) {
  const [dragOver, setDragOver] = useState(false)
  const [uploading, setUploading] = useState(false)

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    const imageFiles = files.filter(file => file.type.startsWith('image/'))
    
    if (imageFiles.length > 0) {
      handleFileUpload(imageFiles)
    }
  }, [])

  const handleFileUpload = async (files: File[]) => {
    setUploading(true)
    try {
      // TODO: Implement actual file upload logic
      console.log('Uploading files:', files)
      
      // Mock upload for now
      setTimeout(() => {
        setUploading(false)
      }, 2000)
    } catch (error) {
      console.error('Upload failed:', error)
      setUploading(false)
    }
  }

  const removeImage = (index: number) => {
    const newValue = value.filter((_, i) => i !== index)
    onChange(PatchEvent.from(set(newValue)))
  }

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newValue = [...value]
    const [movedItem] = newValue.splice(fromIndex, 1)
    newValue.splice(toIndex, 0, movedItem)
    onChange(PatchEvent.from(set(newValue)))
  }

  const setAsHero = (index: number) => {
    const newValue = value.map((img, i) => ({
      ...img,
      isHero: i === index
    }))
    onChange(PatchEvent.from(set(newValue)))
  }

  const getImageStats = () => {
    const total = value.length
    const heroCount = value.filter(img => img.isHero).length
    const withAlt = value.filter(img => img.alt).length
    
    return { total, heroCount, withAlt }
  }

  const stats = getImageStats()

  return (
    <Card padding={4} border radius={2}>
      <Stack space={4}>
        {/* Header with Stats */}
        <Flex align="center" justify="space-between">
          <Text size={2} weight="bold">
            📸 Product Images
          </Text>
          <Flex gap={2}>
            <Badge tone="primary" text={`${stats.total} images`} />
            {stats.heroCount > 0 && <Badge tone="positive" text="Hero set" />}
            {stats.withAlt < stats.total && (
              <Badge tone="caution" text="Missing alt text" />
            )}
          </Flex>
        </Flex>

        {/* Upload Area */}
        <Card
          padding={4}
          tone={dragOver ? 'primary' : 'transparent'}
          border
          style={{
            borderStyle: 'dashed',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onDrop={handleDrop}
          onDragOver={(e) => {
            e.preventDefault()
            setDragOver(true)
          }}
          onDragLeave={() => setDragOver(false)}
        >
          <Stack space={3} align="center">
            <UploadIcon style={{ fontSize: '32px', opacity: 0.5 }} />
            <div style={{ textAlign: 'center' }}>
              <Text size={2} weight="medium">
                {uploading ? 'Uploading...' : 'Drop images here or click to upload'}
              </Text>
              <Text size={1} muted>
                Supports JPG, PNG, WebP • Max 10MB per image
              </Text>
            </div>
            <Button
              text="Choose Files"
              tone="primary"
              mode="default"
              disabled={uploading}
              onClick={() => {
                const input = document.createElement('input')
                input.type = 'file'
                input.accept = 'image/*'
                input.multiple = true
                input.onchange = (e) => {
                  const files = Array.from((e.target as HTMLInputElement).files || [])
                  handleFileUpload(files)
                }
                input.click()
              }}
            />
          </Stack>
        </Card>

        {/* Image Guidelines */}
        <Card padding={3} tone="transparent">
          <Stack space={2}>
            <Text size={1} weight="medium">📋 Image Guidelines</Text>
            <Grid columns={2} gap={2}>
              <div>
                <Text size={1} muted>• First image becomes hero</Text>
                <Text size={1} muted>• Minimum 1200px width</Text>
                <Text size={1} muted>• Square aspect ratio preferred</Text>
              </div>
              <div>
                <Text size={1} muted>• Always add alt text</Text>
                <Text size={1} muted>• Use descriptive filenames</Text>
                <Text size={1} muted>• Optimize for web (WebP)</Text>
              </div>
            </Grid>
          </Stack>
        </Card>

        {/* Image Grid */}
        {value.length > 0 && (
          <Stack space={3}>
            <Text size={1} weight="medium">
              Current Images ({value.length})
            </Text>
            
            <Grid columns={[2, 3, 4]} gap={3}>
              {value.map((image, index) => (
                <ImageCard
                  key={image._key || index}
                  image={image}
                  index={index}
                  isHero={image.isHero}
                  onRemove={() => removeImage(index)}
                  onSetHero={() => setAsHero(index)}
                  onMoveUp={index > 0 ? () => moveImage(index, index - 1) : undefined}
                  onMoveDown={index < value.length - 1 ? () => moveImage(index, index + 1) : undefined}
                />
              ))}
            </Grid>
          </Stack>
        )}

        {/* Empty State */}
        {value.length === 0 && (
          <Card padding={4} tone="transparent" border>
            <Stack space={3} align="center">
              <ImageIcon style={{ fontSize: '48px', opacity: 0.3 }} />
              <div style={{ textAlign: 'center' }}>
                <Text size={2} weight="medium">
                  No images uploaded yet
                </Text>
                <Text size={1} muted>
                  Add high-quality product images to showcase your luxury items
                </Text>
              </div>
            </Stack>
          </Card>
        )}

        {/* Image Quality Tips */}
        {value.length > 0 && (
          <Card padding={3} tone="primary">
            <Stack space={2}>
              <Text size={1} weight="medium">💡 Pro Tips</Text>
              <Text size={1}>
                • Use professional lighting for luxury products
                • Include detail shots of craftsmanship
                • Show the product from multiple angles
                • Consider lifestyle shots for context
              </Text>
            </Stack>
          </Card>
        )}
      </Stack>
    </Card>
  )
}

function ImageCard({
  image,
  index,
  isHero,
  onRemove,
  onSetHero,
  onMoveUp,
  onMoveDown
}: {
  image: any
  index: number
  isHero?: boolean
  onRemove: () => void
  onSetHero: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
}) {
  const [showActions, setShowActions] = useState(false)

  return (
    <Card
      padding={0}
      radius={2}
      shadow={1}
      style={{ position: 'relative' }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Image */}
      <div style={{ 
        position: 'relative', 
        aspectRatio: '1',
        overflow: 'hidden',
        borderRadius: '4px 4px 0 0'
      }}>
        {image.asset ? (
          <img
            src={`${image.asset.url}?w=300&h=300&fit=crop`}
            alt={image.alt || 'Product image'}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        ) : (
          <div style={{
            width: '100%',
            height: '100%',
            backgroundColor: '#f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <ImageIcon style={{ fontSize: '32px', opacity: 0.3 }} />
          </div>
        )}

        {/* Badges */}
        <div style={{
          position: 'absolute',
          top: '8px',
          left: '8px',
          display: 'flex',
          gap: '4px'
        }}>
          {isHero && <Badge tone="positive" text="Hero" />}
          {index === 0 && !isHero && <Badge tone="primary" text="First" />}
        </div>

        {/* Actions Overlay */}
        {showActions && (
          <div style={{
            position: 'absolute',
            inset: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}>
            <Button
              icon={EyeOpenIcon}
              mode="default"
              tone="primary"
              onClick={() => {
                // TODO: Open image preview
                console.log('Preview image')
              }}
            />
            {!isHero && (
              <Button
                text="Set Hero"
                mode="default"
                tone="positive"
                onClick={onSetHero}
              />
            )}
            <Button
              icon={TrashIcon}
              mode="default"
              tone="critical"
              onClick={onRemove}
            />
          </div>
        )}
      </div>

      {/* Image Info */}
      <Stack space={2} padding={2}>
        <Text size={1} weight="medium">
          Image {index + 1}
        </Text>
        
        {image.alt ? (
          <Text size={0} muted>
            Alt: {image.alt}
          </Text>
        ) : (
          <Text size={0} style={{ color: '#cc0000' }}>
            ⚠️ Missing alt text
          </Text>
        )}

        {/* Move buttons */}
        <Flex gap={1}>
          {onMoveUp && (
            <Button
              text="↑"
              mode="ghost"
              fontSize={0}
              onClick={onMoveUp}
            />
          )}
          {onMoveDown && (
            <Button
              text="↓"
              mode="ghost"
              fontSize={0}
              onClick={onMoveDown}
            />
          )}
        </Flex>
      </Stack>
    </Card>
  )
}
