import React, { useState } from 'react'
import { Card, Text, Flex, Badge, Stack, Button, Grid } from '@sanity/ui'
import { useDocumentOperation, useFormValue, useClient } from 'sanity'
import { EyeOpenIcon, EditIcon, PublishIcon, UnpublishIcon } from '@sanity/icons'

interface ProductPreviewProps {
  document: {
    displayed: any
  }
}

export function ProductPreview({ document }: ProductPreviewProps) {
  const { displayed } = document
  const { patch, publish, unpublish } = useDocumentOperation(displayed._id, displayed._type)
  const client = useClient()
  const [isUpdating, setIsUpdating] = useState(false)

  // Get current form values
  const name = useFormValue(['name']) as string
  const brand = useFormValue(['brand']) as any
  const pricing = useFormValue(['pricing']) as any
  const inventory = useFormValue(['inventory']) as any
  const images = useFormValue(['images']) as any[]
  const isAvailable = useFormValue(['isAvailable']) as boolean
  const isFeatured = useFormValue(['isFeatured']) as boolean
  const isNew = useFormValue(['isNew']) as boolean
  const category = useFormValue(['category']) as any
  const collection = useFormValue(['collection']) as any

  const stockStatus = inventory?.stock > 0 ? 'In Stock' : 'Out of Stock'
  const stockColor = inventory?.stock > 0 ? 'positive' : 'critical'

  const price = pricing?.basePrice ? `$${pricing.basePrice.toLocaleString()}` : 'Price TBD'
  const originalPrice = pricing?.originalPrice ? `$${pricing.originalPrice.toLocaleString()}` : null

  const handleQuickUpdate = async (updates: any) => {
    setIsUpdating(true)
    try {
      await patch.execute([{ set: updates }])
    } catch (error) {
      console.error('Failed to update product:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const getCompletionScore = () => {
    let score = 0
    const checks = [
      { field: name, weight: 20, label: 'Product Name' },
      { field: brand, weight: 15, label: 'Brand' },
      { field: pricing?.basePrice, weight: 15, label: 'Price' },
      { field: images?.length > 0, weight: 20, label: 'Images' },
      { field: category, weight: 10, label: 'Category' },
      { field: inventory?.stock !== undefined, weight: 10, label: 'Stock' },
      { field: inventory?.sku, weight: 10, label: 'SKU' }
    ]

    checks.forEach(check => {
      if (check.field) score += check.weight
    })

    return { score, checks }
  }

  const { score, checks } = getCompletionScore()

  return (
    <Stack space={4}>
      {/* Header with Status */}
      <Card padding={4} tone={isAvailable ? 'positive' : 'caution'}>
        <Stack space={3}>
          <Flex align="center" justify="space-between">
            <Text size={3} weight="bold">
              📦 Product Overview
            </Text>
            <Flex gap={2}>
              {isFeatured && <Badge tone="primary">⭐ Featured</Badge>}
              {isNew && <Badge tone="positive">🆕 New</Badge>}
              <Badge tone={stockColor}>{stockStatus}</Badge>
            </Flex>
          </Flex>

          <div>
            <Text size={4} weight="bold">
              {name || 'Untitled Product'}
            </Text>
            {brand?.name && (
              <Text size={2} muted>
                by {brand.name}
              </Text>
            )}
          </div>
        </Stack>
      </Card>

      {/* Completion Score */}
      <Card padding={4}>
        <Stack space={3}>
          <Flex align="center" justify="space-between">
            <Text size={2} weight="bold">
              📊 Completion Score
            </Text>
            <Badge
              tone={score >= 80 ? 'positive' : score >= 60 ? 'caution' : 'critical'}
            >
              {score}%
            </Badge>
          </Flex>

          <div style={{
            width: '100%',
            height: '8px',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${score}%`,
              height: '100%',
              backgroundColor: score >= 80 ? '#00cc00' : score >= 60 ? '#ff9900' : '#cc0000',
              transition: 'width 0.3s ease'
            }} />
          </div>

          <Grid columns={2} gap={2}>
            {checks.map((check, index) => (
              <Flex key={index} align="center" gap={2}>
                <span style={{ color: check.field ? '#00cc00' : '#cc0000' }}>
                  {check.field ? '✓' : '✗'}
                </span>
                <Text size={1} style={{ color: check.field ? 'inherit' : '#cc0000' }}>
                  {check.label}
                </Text>
              </Flex>
            ))}
          </Grid>
        </Stack>
      </Card>

      {/* Product Details */}
      <Grid columns={2} gap={3}>
        <Card padding={3}>
          <Stack space={2}>
            <Text size={1} weight="bold">💰 Pricing</Text>
            <Flex align="center" gap={2}>
              <Text size={2} weight="semibold">
                {price}
              </Text>
              {originalPrice && (
                <Text size={1} style={{ textDecoration: 'line-through' }} muted>
                  {originalPrice}
                </Text>
              )}
            </Flex>
            {pricing?.priceOnRequest && (
              <Badge tone="primary">Price on Request</Badge>
            )}
          </Stack>
        </Card>

        <Card padding={3}>
          <Stack space={2}>
            <Text size={1} weight="bold">📦 Inventory</Text>
            <Flex align="center" gap={2}>
              <Text size={2}>
                {inventory?.stock || 0} units
              </Text>
              {inventory?.lowStockThreshold && inventory?.stock <= inventory.lowStockThreshold && (
                <Badge tone="caution">⚠️ Low Stock</Badge>
              )}
            </Flex>
            {inventory?.sku && (
              <Text size={1} muted>
                SKU: {inventory.sku}
              </Text>
            )}
          </Stack>
        </Card>
      </Grid>

      {/* Media & Content */}
      <Card padding={3}>
        <Stack space={2}>
          <Text size={1} weight="bold">🖼️ Media & Content</Text>
          <Grid columns={3} gap={2}>
            <div>
              <Text size={1} muted>Images</Text>
              <Text size={2}>{images?.length || 0}</Text>
            </div>
            <div>
              <Text size={1} muted>Category</Text>
              <Text size={2}>{category?.name || 'None'}</Text>
            </div>
            <div>
              <Text size={1} muted>Collection</Text>
              <Text size={2}>{collection?.name || 'None'}</Text>
            </div>
          </Grid>
        </Stack>
      </Card>

      {/* Quick Actions */}
      <Card padding={4}>
        <Stack space={3}>
          <Text size={2} weight="bold">⚡ Quick Actions</Text>

          <Grid columns={2} gap={2}>
            <Button
              text={isFeatured ? "Remove Featured" : "Mark Featured"}
              tone={isFeatured ? "default" : "primary"}
              mode="default"
              onClick={() => handleQuickUpdate({ isFeatured: !isFeatured })}
              disabled={isUpdating}
            />

            <Button
              text={isAvailable ? "Hide Product" : "Show Product"}
              tone={isAvailable ? "critical" : "positive"}
              mode="default"
              onClick={() => handleQuickUpdate({ isAvailable: !isAvailable })}
              disabled={isUpdating}
            />

            <Button
              text={isNew ? "Remove New Badge" : "Mark as New"}
              tone={isNew ? "default" : "positive"}
              mode="default"
              onClick={() => handleQuickUpdate({ isNew: !isNew })}
              disabled={isUpdating}
            />

            <Button
              text="Duplicate Product"
              tone="primary"
              mode="ghost"
              onClick={() => {
                // TODO: Implement duplication logic
                console.log('Duplicate product')
              }}
            />
          </Grid>
        </Stack>
      </Card>
    </Stack>
  )
}

// Custom input component for better UX
export function PriceInput(props: any) {
  const { value, onChange, elementProps } = props
  
  return (
    <div style={{ position: 'relative' }}>
      <span style={{
        position: 'absolute',
        left: '12px',
        top: '50%',
        transform: 'translateY(-50%)',
        color: '#666',
        fontSize: '14px',
        pointerEvents: 'none'
      }}>
        $
      </span>
      <input
        {...elementProps}
        type="number"
        value={value || ''}
        onChange={(e) => onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
        style={{
          ...elementProps.style,
          paddingLeft: '24px'
        }}
        placeholder="0.00"
        min="0"
        step="0.01"
      />
    </div>
  )
}

// Stock level indicator
export function StockIndicator(props: any) {
  const { value } = props
  const stock = value || 0
  
  let color = '#cc0000' // Red for out of stock
  let text = 'Out of Stock'
  
  if (stock > 10) {
    color = '#00cc00' // Green for good stock
    text = 'In Stock'
  } else if (stock > 0) {
    color = '#ff9900' // Orange for low stock
    text = 'Low Stock'
  }
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      gap: '8px',
      padding: '4px 8px',
      borderRadius: '4px',
      backgroundColor: `${color}20`,
      border: `1px solid ${color}40`
    }}>
      <div style={{
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: color
      }} />
      <span style={{
        fontSize: '12px',
        fontWeight: '500',
        color: color
      }}>
        {text} ({stock} units)
      </span>
    </div>
  )
}
