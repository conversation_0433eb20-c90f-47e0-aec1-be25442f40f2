import React, { useState, useEffect } from 'react'
import { Card, Text, Flex, Grid, Stack, Button, TextInput } from '@sanity/ui'
import { SearchIcon, FilterIcon, ImageIcon } from '@sanity/icons'
import { useClient } from 'sanity'

interface MediaItem {
  _id: string
  _type: string
  asset: {
    _ref: string
    url: string
    metadata: {
      dimensions: {
        width: number
        height: number
      }
      lqip: string
    }
  }
  alt?: string
  caption?: string
  tags?: string[]
}

export function MediaLibrary() {
  const client = useClient()
  const [images, setImages] = useState<MediaItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  useEffect(() => {
    fetchImages()
  }, [])

  const fetchImages = async () => {
    try {
      setLoading(true)
      const query = `*[_type == "sanity.imageAsset"] | order(_createdAt desc) [0...50] {
        _id,
        _type,
        url,
        metadata,
        "references": *[references(^._id)] {
          _type,
          _id,
          name,
          title,
          alt
        }
      }`
      
      const result = await client.fetch(query)
      setImages(result)
    } catch (error) {
      console.error('Error fetching images:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredImages = images.filter(image => {
    const matchesSearch = !searchTerm || 
      image.alt?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      image.caption?.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Add category filtering logic here if needed
    return matchesSearch
  })

  const categories = [
    { value: 'all', label: 'All Images' },
    { value: 'products', label: 'Product Images' },
    { value: 'brands', label: 'Brand Assets' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'editorial', label: 'Editorial' }
  ]

  if (loading) {
    return (
      <Card padding={4}>
        <Text>Loading media library...</Text>
      </Card>
    )
  }

  return (
    <Card padding={4}>
      <Stack space={4}>
        {/* Header */}
        <Flex align="center" justify="space-between">
          <Text size={3} weight="bold">
            Media Library
          </Text>
          <Button
            text="Upload New"
            tone="primary"
            icon={ImageIcon}
            onClick={() => {
              // Trigger file upload
              const input = document.createElement('input')
              input.type = 'file'
              input.accept = 'image/*'
              input.multiple = true
              input.click()
            }}
          />
        </Flex>

        {/* Filters */}
        <Flex gap={3} align="center">
          <TextInput
            icon={SearchIcon}
            placeholder="Search images..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.currentTarget.value)}
            style={{ minWidth: '300px' }}
          />
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              background: 'white'
            }}
          >
            {categories.map(cat => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))}
          </select>
        </Flex>

        {/* Stats */}
        <Flex gap={4}>
          <Text size={1} muted>
            Total: {images.length} images
          </Text>
          <Text size={1} muted>
            Filtered: {filteredImages.length} images
          </Text>
        </Flex>

        {/* Image Grid */}
        <Grid columns={[2, 3, 4, 5]} gap={3}>
          {filteredImages.map((image) => (
            <ImageCard key={image._id} image={image} />
          ))}
        </Grid>

        {filteredImages.length === 0 && (
          <Card padding={4} tone="transparent" border>
            <Flex direction="column" align="center" gap={3}>
              <ImageIcon style={{ fontSize: '48px', opacity: 0.3 }} />
              <Text size={2} muted>
                No images found
              </Text>
              {searchTerm && (
                <Button
                  text="Clear search"
                  mode="ghost"
                  onClick={() => setSearchTerm('')}
                />
              )}
            </Flex>
          </Card>
        )}
      </Stack>
    </Card>
  )
}

function ImageCard({ image }: { image: MediaItem }) {
  const [showDetails, setShowDetails] = useState(false)
  
  return (
    <Card
      padding={0}
      radius={2}
      shadow={1}
      style={{ cursor: 'pointer' }}
      onClick={() => setShowDetails(!showDetails)}
    >
      <div style={{ position: 'relative', aspectRatio: '1' }}>
        <img
          src={image.asset?.url || image.metadata?.lqip}
          alt={image.alt || 'Image'}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: '4px 4px 0 0'
          }}
        />
        
        {/* Overlay with info */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
          color: 'white',
          padding: '8px',
          fontSize: '12px'
        }}>
          <div>
            {image.metadata?.dimensions && (
              <span>
                {image.metadata.dimensions.width} × {image.metadata.dimensions.height}
              </span>
            )}
          </div>
        </div>
      </div>
      
      {showDetails && (
        <Stack space={2} padding={2}>
          <Text size={1} weight="medium">
            {image.alt || 'Untitled'}
          </Text>
          {image.caption && (
            <Text size={0} muted>
              {image.caption}
            </Text>
          )}
          <Flex gap={1} wrap="wrap">
            {image.tags?.map(tag => (
              <span
                key={tag}
                style={{
                  fontSize: '10px',
                  padding: '2px 6px',
                  background: '#f0f0f0',
                  borderRadius: '12px',
                  color: '#666'
                }}
              >
                {tag}
              </span>
            ))}
          </Flex>
        </Stack>
      )}
    </Card>
  )
}

// Usage statistics component
export function MediaUsageStats() {
  const client = useClient()
  const [stats, setStats] = useState({
    totalImages: 0,
    usedImages: 0,
    unusedImages: 0,
    totalSize: 0
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const query = `{
        "totalImages": count(*[_type == "sanity.imageAsset"]),
        "usedImages": count(*[_type == "sanity.imageAsset" && count(*[references(^._id)]) > 0]),
        "unusedImages": count(*[_type == "sanity.imageAsset" && count(*[references(^._id)]) == 0])
      }`
      
      const result = await client.fetch(query)
      setStats(result)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  return (
    <Card padding={4} tone="transparent">
      <Stack space={3}>
        <Text size={2} weight="bold">
          Media Statistics
        </Text>
        
        <Grid columns={3} gap={3}>
          <Card padding={3} tone="primary">
            <Stack space={1}>
              <Text size={3} weight="bold">
                {stats.totalImages}
              </Text>
              <Text size={1}>
                Total Images
              </Text>
            </Stack>
          </Card>
          
          <Card padding={3} tone="positive">
            <Stack space={1}>
              <Text size={3} weight="bold">
                {stats.usedImages}
              </Text>
              <Text size={1}>
                In Use
              </Text>
            </Stack>
          </Card>
          
          <Card padding={3} tone="caution">
            <Stack space={1}>
              <Text size={3} weight="bold">
                {stats.unusedImages}
              </Text>
              <Text size={1}>
                Unused
              </Text>
            </Stack>
          </Card>
        </Grid>
      </Stack>
    </Card>
  )
}
