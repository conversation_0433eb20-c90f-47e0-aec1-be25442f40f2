import React, { useState, useEffect } from 'react'
import { Card, Text, Flex, Grid, Stack, Badge } from '@sanity/ui'
import { ChartUpwardIcon, EyeOpenIcon, EditIcon } from '@sanity/icons'
import { useClient } from 'sanity'

interface AnalyticsData {
  totalProducts: number
  publishedProducts: number
  draftProducts: number
  featuredProducts: number
  outOfStockProducts: number
  recentActivity: any[]
  popularProducts: any[]
  contentStats: {
    blogPosts: number
    landingPages: number
    collections: number
    brands: number
  }
}

export function AnalyticsDashboard() {
  const client = useClient()
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      
      const queries = {
        totalProducts: `count(*[_type == "product"])`,
        publishedProducts: `count(*[_type == "product" && isAvailable == true])`,
        draftProducts: `count(*[_type == "product" && isAvailable != true])`,
        featuredProducts: `count(*[_type == "product" && isFeatured == true])`,
        outOfStockProducts: `count(*[_type == "product" && inventory.stock <= 0])`,
        
        recentActivity: `*[_type in ["product", "blogPost", "landingPage", "collection"]] | order(_updatedAt desc) [0...10] {
          _id,
          _type,
          _updatedAt,
          name,
          title,
          isAvailable,
          isPublished
        }`,
        
        popularProducts: `*[_type == "product" && isAvailable == true] | order(isFeatured desc, _createdAt desc) [0...5] {
          _id,
          name,
          "brand": brand->name,
          "price": pricing.basePrice,
          "stock": inventory.stock,
          isFeatured,
          isNew
        }`,
        
        contentStats: `{
          "blogPosts": count(*[_type == "blogPost"]),
          "landingPages": count(*[_type == "landingPage"]),
          "collections": count(*[_type == "collection"]),
          "brands": count(*[_type == "brand"])
        }`
      }

      const results = await Promise.all([
        client.fetch(queries.totalProducts),
        client.fetch(queries.publishedProducts),
        client.fetch(queries.draftProducts),
        client.fetch(queries.featuredProducts),
        client.fetch(queries.outOfStockProducts),
        client.fetch(queries.recentActivity),
        client.fetch(queries.popularProducts),
        client.fetch(queries.contentStats)
      ])

      setData({
        totalProducts: results[0],
        publishedProducts: results[1],
        draftProducts: results[2],
        featuredProducts: results[3],
        outOfStockProducts: results[4],
        recentActivity: results[5],
        popularProducts: results[6],
        contentStats: results[7]
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card padding={4}>
        <Text>Loading analytics...</Text>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card padding={4}>
        <Text>Failed to load analytics data</Text>
      </Card>
    )
  }

  return (
    <Stack space={4}>
      {/* Header */}
      <Flex align="center" justify="space-between">
        <Text size={3} weight="bold">
          Analytics Dashboard
        </Text>
        <Text size={1} muted>
          Last updated: {new Date().toLocaleString()}
        </Text>
      </Flex>

      {/* Key Metrics */}
      <Grid columns={[2, 3, 5]} gap={3}>
        <MetricCard
          title="Total Products"
          value={data.totalProducts}
          icon="📦"
          color="primary"
        />
        <MetricCard
          title="Published"
          value={data.publishedProducts}
          icon="✅"
          color="positive"
          subtitle={`${Math.round((data.publishedProducts / data.totalProducts) * 100)}% of total`}
        />
        <MetricCard
          title="Drafts"
          value={data.draftProducts}
          icon="📝"
          color="caution"
        />
        <MetricCard
          title="Featured"
          value={data.featuredProducts}
          icon="⭐"
          color="primary"
        />
        <MetricCard
          title="Out of Stock"
          value={data.outOfStockProducts}
          icon="⚠️"
          color="critical"
        />
      </Grid>

      {/* Content Overview */}
      <Card padding={4}>
        <Stack space={3}>
          <Text size={2} weight="bold">
            Content Overview
          </Text>
          <Grid columns={4} gap={3}>
            <ContentStat
              label="Blog Posts"
              value={data.contentStats.blogPosts}
              icon="📝"
            />
            <ContentStat
              label="Landing Pages"
              value={data.contentStats.landingPages}
              icon="🎯"
            />
            <ContentStat
              label="Collections"
              value={data.contentStats.collections}
              icon="📚"
            />
            <ContentStat
              label="Brands"
              value={data.contentStats.brands}
              icon="🏢"
            />
          </Grid>
        </Stack>
      </Card>

      <Grid columns={[1, 2]} gap={4}>
        {/* Recent Activity */}
        <Card padding={4}>
          <Stack space={3}>
            <Flex align="center" gap={2}>
              <EditIcon />
              <Text size={2} weight="bold">
                Recent Activity
              </Text>
            </Flex>
            
            <Stack space={2}>
              {data.recentActivity.map((item) => (
                <ActivityItem key={item._id} item={item} />
              ))}
            </Stack>
          </Stack>
        </Card>

        {/* Popular Products */}
        <Card padding={4}>
          <Stack space={3}>
            <Flex align="center" gap={2}>
              <ChartUpwardIcon />
              <Text size={2} weight="bold">
                Top Products
              </Text>
            </Flex>
            
            <Stack space={2}>
              {data.popularProducts.map((product) => (
                <ProductItem key={product._id} product={product} />
              ))}
            </Stack>
          </Stack>
        </Card>
      </Grid>
    </Stack>
  )
}

function MetricCard({ 
  title, 
  value, 
  icon, 
  color = 'default', 
  subtitle 
}: {
  title: string
  value: number
  icon: string
  color?: string
  subtitle?: string
}) {
  return (
    <Card padding={3} tone={color as any}>
      <Stack space={2}>
        <Flex align="center" gap={2}>
          <span style={{ fontSize: '20px' }}>{icon}</span>
          <Text size={1} weight="medium">
            {title}
          </Text>
        </Flex>
        <Text size={3} weight="bold">
          {value.toLocaleString()}
        </Text>
        {subtitle && (
          <Text size={0} muted>
            {subtitle}
          </Text>
        )}
      </Stack>
    </Card>
  )
}

function ContentStat({ 
  label, 
  value, 
  icon 
}: {
  label: string
  value: number
  icon: string
}) {
  return (
    <Flex align="center" gap={2}>
      <span style={{ fontSize: '16px' }}>{icon}</span>
      <div>
        <Text size={2} weight="bold">
          {value}
        </Text>
        <Text size={1} muted>
          {label}
        </Text>
      </div>
    </Flex>
  )
}

function ActivityItem({ item }: { item: any }) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product': return '📦'
      case 'blogPost': return '📝'
      case 'landingPage': return '🎯'
      case 'collection': return '📚'
      default: return '📄'
    }
  }

  const getStatusBadge = (item: any) => {
    if (item._type === 'product') {
      return item.isAvailable ? 
        <Badge tone="positive">Published</Badge> :
        <Badge tone="default">Draft</Badge>
    }
    if (item._type === 'blogPost' || item._type === 'landingPage') {
      return item.isPublished ? 
        <Badge tone="positive">Published</Badge> :
        <Badge tone="default">Draft</Badge>
    }
    return null
  }

  return (
    <Card padding={2} tone="transparent" border>
      <Flex align="center" justify="space-between">
        <Flex align="center" gap={2}>
          <span>{getTypeIcon(item._type)}</span>
          <div>
            <Text size={1} weight="medium">
              {item.name || item.title}
            </Text>
            <Text size={0} muted>
              {new Date(item._updatedAt).toLocaleDateString()}
            </Text>
          </div>
        </Flex>
        {getStatusBadge(item)}
      </Flex>
    </Card>
  )
}

function ProductItem({ product }: { product: any }) {
  const stockStatus = product.stock > 0 ? 'In Stock' : 'Out of Stock'
  const stockColor = product.stock > 0 ? 'positive' : 'critical'

  return (
    <Card padding={2} tone="transparent" border>
      <Flex align="center" justify="space-between">
        <div>
          <Flex align="center" gap={2}>
            <Text size={1} weight="medium">
              {product.name}
            </Text>
            {product.isFeatured && <Badge tone="primary">Featured</Badge>}
            {product.isNew && <Badge tone="positive">New</Badge>}
          </Flex>
          <Text size={0} muted>
            {product.brand} • ${product.price?.toLocaleString()}
          </Text>
        </div>
        <Badge tone={stockColor}>{stockStatus}</Badge>
      </Flex>
    </Card>
  )
}
