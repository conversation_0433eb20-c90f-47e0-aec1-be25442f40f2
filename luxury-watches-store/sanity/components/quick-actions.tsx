import React from 'react'
import { Card, Text, Flex, Stack, Button, Grid, Badge } from '@sanity/ui'
import { 
  PlusIcon, 
  DocumentTextIcon, 
  ShoppingBagIcon,
  PhotoIcon,
  StarIcon,
  EyeOpenIcon
} from '@sanity/icons'

export function QuickActions() {
  const quickActions = [
    {
      title: 'Add New Product',
      description: 'Create a luxury timepiece or jewelry item',
      icon: '📦',
      color: 'primary',
      href: '/desk/product;new',
      category: 'Products'
    },
    {
      title: 'Write Blog Post',
      description: 'Share luxury insights and stories',
      icon: '✍️',
      color: 'positive',
      href: '/desk/blogPost;new',
      category: 'Content'
    },
    {
      title: 'Create Landing Page',
      description: 'Build high-converting pages',
      icon: '🎯',
      color: 'caution',
      href: '/desk/landingPage;new',
      category: 'Content'
    },
    {
      title: 'Add Brand',
      description: 'Register a new luxury brand',
      icon: '🏢',
      color: 'primary',
      href: '/desk/brand;new',
      category: 'Catalog'
    },
    {
      title: 'Create Collection',
      description: 'Curate a product collection',
      icon: '📚',
      color: 'positive',
      href: '/desk/collection;new',
      category: 'Catalog'
    },
    {
      title: 'Upload Media',
      description: 'Add product images and assets',
      icon: '🖼️',
      color: 'caution',
      href: '/desk/media',
      category: 'Media'
    }
  ]

  const recentTemplates = [
    {
      name: 'Luxury Watch Product',
      description: 'Pre-filled template for timepieces',
      icon: '⌚',
      action: () => console.log('Create from watch template')
    },
    {
      name: 'Jewelry Item',
      description: 'Template for fine jewelry',
      icon: '💎',
      action: () => console.log('Create from jewelry template')
    },
    {
      name: 'Brand Story Page',
      description: 'Heritage and craftsmanship content',
      icon: '📖',
      action: () => console.log('Create brand story')
    },
    {
      name: 'Product Launch',
      description: 'New arrival announcement',
      icon: '🚀',
      action: () => console.log('Create product launch')
    }
  ]

  return (
    <Stack space={4}>
      {/* Quick Actions Header */}
      <Card padding={4} tone="primary">
        <Flex align="center" gap={3}>
          <span style={{ fontSize: '24px' }}>⚡</span>
          <div>
            <Text size={3} weight="bold">
              Quick Actions
            </Text>
            <Text size={1}>
              Fast-track your content creation workflow
            </Text>
          </div>
        </Flex>
      </Card>

      {/* Main Actions Grid */}
      <Grid columns={[1, 2, 3]} gap={3}>
        {quickActions.map((action, index) => (
          <QuickActionCard key={index} action={action} />
        ))}
      </Grid>

      {/* Templates Section */}
      <Card padding={4}>
        <Stack space={3}>
          <Flex align="center" gap={2}>
            <span style={{ fontSize: '20px' }}>📋</span>
            <Text size={2} weight="bold">
              Content Templates
            </Text>
          </Flex>
          
          <Text size={1} muted>
            Start with pre-configured templates for faster content creation
          </Text>
          
          <Grid columns={[1, 2]} gap={3}>
            {recentTemplates.map((template, index) => (
              <TemplateCard key={index} template={template} />
            ))}
          </Grid>
        </Stack>
      </Card>

      {/* Workflow Tips */}
      <Card padding={4} tone="transparent">
        <Stack space={3}>
          <Flex align="center" gap={2}>
            <span style={{ fontSize: '20px' }}>💡</span>
            <Text size={2} weight="bold">
              Workflow Tips
            </Text>
          </Flex>
          
          <Stack space={2}>
            <WorkflowTip
              icon="📸"
              title="Image Guidelines"
              description="Upload high-resolution images (min 1200px) with proper alt text for SEO"
            />
            <WorkflowTip
              icon="🏷️"
              title="Product Organization"
              description="Always assign products to brands, categories, and collections for better discoverability"
            />
            <WorkflowTip
              icon="✅"
              title="Content Review"
              description="Use the preview feature to check how content appears on the website before publishing"
            />
            <WorkflowTip
              icon="📊"
              title="Performance Tracking"
              description="Monitor your content performance through the Analytics dashboard"
            />
          </Stack>
        </Stack>
      </Card>
    </Stack>
  )
}

function QuickActionCard({ action }: { action: any }) {
  const handleClick = () => {
    if (action.href) {
      // Navigate to the specified route
      window.location.href = action.href
    }
  }

  return (
    <Card
      padding={4}
      tone={action.color}
      style={{ cursor: 'pointer' }}
      onClick={handleClick}
    >
      <Stack space={3}>
        <Flex align="center" justify="space-between">
          <span style={{ fontSize: '24px' }}>{action.icon}</span>
          <Badge tone="default" text={action.category} />
        </Flex>
        
        <div>
          <Text size={2} weight="bold">
            {action.title}
          </Text>
          <Text size={1} muted>
            {action.description}
          </Text>
        </div>
        
        <Flex justify="flex-end">
          <Button
            text="Create"
            mode="default"
            tone="primary"
            onClick={(e) => {
              e.stopPropagation()
              handleClick()
            }}
          />
        </Flex>
      </Stack>
    </Card>
  )
}

function TemplateCard({ template }: { template: any }) {
  return (
    <Card
      padding={3}
      tone="transparent"
      border
      style={{ cursor: 'pointer' }}
      onClick={template.action}
    >
      <Flex align="center" gap={3}>
        <span style={{ fontSize: '20px' }}>{template.icon}</span>
        <div style={{ flex: 1 }}>
          <Text size={1} weight="medium">
            {template.name}
          </Text>
          <Text size={0} muted>
            {template.description}
          </Text>
        </div>
        <Button
          text="Use"
          mode="ghost"
          fontSize={0}
          onClick={(e) => {
            e.stopPropagation()
            template.action()
          }}
        />
      </Flex>
    </Card>
  )
}

function WorkflowTip({ 
  icon, 
  title, 
  description 
}: {
  icon: string
  title: string
  description: string
}) {
  return (
    <Card padding={3} tone="transparent" border>
      <Flex align="flex-start" gap={3}>
        <span style={{ fontSize: '16px' }}>{icon}</span>
        <div>
          <Text size={1} weight="medium">
            {title}
          </Text>
          <Text size={0} muted>
            {description}
          </Text>
        </div>
      </Flex>
    </Card>
  )
}

// Keyboard shortcuts component
export function KeyboardShortcuts() {
  const shortcuts = [
    { key: 'Ctrl + N', action: 'New document' },
    { key: 'Ctrl + S', action: 'Save document' },
    { key: 'Ctrl + P', action: 'Publish document' },
    { key: 'Ctrl + /', action: 'Search' },
    { key: 'Ctrl + K', action: 'Command palette' },
    { key: 'Esc', action: 'Close modal/panel' }
  ]

  return (
    <Card padding={4}>
      <Stack space={3}>
        <Text size={2} weight="bold">
          ⌨️ Keyboard Shortcuts
        </Text>
        
        <Grid columns={2} gap={2}>
          {shortcuts.map((shortcut, index) => (
            <Flex key={index} align="center" justify="space-between">
              <Text size={1}>{shortcut.action}</Text>
              <Badge tone="default" text={shortcut.key} />
            </Flex>
          ))}
        </Grid>
      </Stack>
    </Card>
  )
}
