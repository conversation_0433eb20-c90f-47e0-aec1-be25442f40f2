# Sanity CMS Setup Guide

## 🎯 Overview

This guide will help you set up Sanity CMS for the Luxury Watches Store. Sanity will provide a powerful content management system for products, blog posts, and site content.

## 📋 Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- Sanity account (free at [sanity.io](https://sanity.io))

## 🚀 Setup Steps

### 1. Create Sanity Project

1. **Sign up/Login to Sanity**
   - Go to [sanity.io](https://sanity.io)
   - Create an account or login

2. **Create New Project**
   ```bash
   npx @sanity/cli init
   ```
   - Choose "Create new project"
   - Give your project a name (e.g., "Luxury Watches Store")
   - Choose "Blog" template or "Clean project"
   - Select "TypeScript"

3. **Get Project Details**
   - Note your Project ID
   - Note your Dataset name (usually "production")

### 2. Configure Environment Variables

1. **Copy environment template**
   ```bash
   cp .env.local.example .env.local
   ```

2. **Update .env.local with your Sanity details**
   ```env
   NEXT_PUBLIC_SANITY_PROJECT_ID=your-actual-project-id
   NEXT_PUBLIC_SANITY_DATASET=production
   SANITY_API_TOKEN=your-api-token
   ```

3. **Get API Token (for write operations)**
   - Go to [manage.sanity.io](https://manage.sanity.io)
   - Select your project
   - Go to "API" tab
   - Create new token with "Editor" permissions
   - Add token to .env.local

### 3. Deploy Sanity Studio

1. **Build and deploy studio**
   ```bash
   npm run build
   npx sanity deploy
   ```

2. **Access Studio**
   - Local: http://localhost:3000/studio
   - Deployed: https://your-project.sanity.studio

### 4. Add Sample Content

#### Products
1. Go to Sanity Studio
2. Create Brands first (Rolex, Patek Philippe, etc.)
3. Create Categories (Dress Watches, Sport Watches, etc.)
4. Create Collections (optional)
5. Create Products with:
   - Name and description
   - Brand reference
   - Category reference
   - Images (upload high-quality watch photos)
   - Price and specifications
   - Stock information

#### Blog Posts
1. Create blog posts with:
   - Title and content
   - Featured image
   - Categories and tags
   - Author information
   - Publication date

#### Site Settings
1. Configure site-wide settings:
   - Site title and description
   - Logo and favicon
   - Contact information
   - Social media links
   - SEO defaults

## 🎨 Content Schemas

The following schemas are already configured:

### Core Schemas
- **Product** - Watch products with specifications
- **Brand** - Watch brands (Rolex, Patek Philippe, etc.)
- **Collection** - Brand collections
- **Category** - Product categories
- **Review** - Customer reviews

### Content Schemas
- **Blog Post** - Blog articles with rich content
- **Page** - Static pages
- **Site Settings** - Global site configuration

## 🖼️ Image Optimization

Sanity provides automatic image optimization:
- WebP conversion
- Responsive sizing
- CDN delivery
- Hotspot cropping

Example usage:
```typescript
import { urlFor } from '@/lib/sanity/client'

// Generate optimized image URL
const imageUrl = urlFor(image)
  .width(800)
  .height(600)
  .format('webp')
  .url()
```

## 🔍 GROQ Queries

Pre-built queries are available in `/src/lib/sanity/client.ts`:

- `allProducts` - All available products
- `productBySlug` - Single product by slug
- `featuredProducts` - Featured products
- `allBrands` - All brands
- `allBlogPosts` - All published blog posts
- `siteSettings` - Site configuration

## 🚀 Going Live

### 1. Production Setup
1. Set production environment variables
2. Configure CORS in Sanity (add your domain)
3. Set up webhooks for content updates
4. Configure CDN caching

### 2. Content Strategy
1. **Products**: Add all watch inventory
2. **Brands**: Complete brand information
3. **Blog**: Regular content updates
4. **SEO**: Optimize all content for search

### 3. Performance
- Enable Sanity CDN
- Configure Next.js ISR (Incremental Static Regeneration)
- Set up proper caching headers
- Monitor Core Web Vitals

## 🛠️ Development Commands

```bash
# Start development server
npm run dev

# Access Sanity Studio locally
# http://localhost:3000/studio

# Deploy Sanity Studio
npx sanity deploy

# Manage Sanity project
npx sanity manage
```

## 📚 Resources

- [Sanity Documentation](https://www.sanity.io/docs)
- [GROQ Query Language](https://www.sanity.io/docs/groq)
- [Next.js + Sanity Guide](https://www.sanity.io/guides/nextjs)
- [Image Optimization](https://www.sanity.io/docs/image-urls)

## 🆘 Troubleshooting

### Common Issues

1. **"Dataset not found" error**
   - Check NEXT_PUBLIC_SANITY_PROJECT_ID
   - Verify dataset name in Sanity dashboard

2. **Images not loading**
   - Verify image URLs in Sanity
   - Check CORS settings
   - Ensure images are published

3. **Studio not accessible**
   - Check if studio is deployed
   - Verify project configuration
   - Clear browser cache

### Support
- Sanity Community: [slack.sanity.io](https://slack.sanity.io)
- Documentation: [sanity.io/docs](https://sanity.io/docs)
- GitHub Issues: Create issue in project repository
