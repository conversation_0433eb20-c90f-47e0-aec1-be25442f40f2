const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: '81887gvx',
  dataset: 'production',
  useCdn: false,
  token: 'skFAEfOTLg3OJZBt5EnDeHVw8FXWn2mbse5U1rXQrkkErmbwoVcnDnMneFBfXtB37n1U8cb3qZyuHq0QZezCjM7Q8euRNUQbE8IRAINbQIM5ZKWzXWTTc4XFtXEcB8U2WXYL0OUUZH8vHf5TppH3iynyVbPI8LpGL6SrRA1kIHQuXNOcgGzd',
  apiVersion: '2024-01-01'
})

async function verifyDataDisplay() {
  try {
    console.log('🔍 Verifying data for website display...\n')
    
    // Check products with brand references
    console.log('📦 PRODUCTS:')
    const products = await client.fetch(`
      *[_type == "product"] {
        _id,
        name,
        "brand": brand->name,
        "brandSlug": brand->slug.current,
        "category": category->name,
        "categorySlug": category->slug.current,
        price,
        isAvailable,
        isFeatured,
        _createdAt
      }
    `)
    
    console.log(`Found ${products.length} products:`)
    products.forEach(p => {
      console.log(`  ✅ ${p.name} - ${p.brand || 'NO BRAND'} - $${p.price} - ${p.category || 'NO CATEGORY'}`)
    })
    
    // Check jewelry with brand references
    console.log('\n💎 JEWELRY:')
    const jewelry = await client.fetch(`
      *[_type == "jewelry"] {
        _id,
        name,
        "brand": brand->name,
        "brandSlug": brand->slug.current,
        "category": category->name,
        "categorySlug": category->slug.current,
        price,
        isAvailable,
        isFeatured,
        _createdAt
      }
    `)
    
    console.log(`Found ${jewelry.length} jewelry items:`)
    jewelry.forEach(j => {
      console.log(`  ✅ ${j.name} - ${j.brand || 'NO BRAND'} - $${j.price} - ${j.category || 'NO CATEGORY'}`)
    })
    
    // Check brands
    console.log('\n🏷️ BRANDS:')
    const brands = await client.fetch(`
      *[_type == "brand"] {
        _id,
        name,
        slug,
        description,
        founded,
        country,
        isLuxury
      }
    `)
    
    console.log(`Found ${brands.length} brands:`)
    brands.forEach(b => {
      console.log(`  ✅ ${b.name} (${b.founded}) - ${b.country}`)
    })
    
    // Check categories
    console.log('\n📂 CATEGORIES:')
    const categories = await client.fetch(`
      *[_type == "category"] {
        _id,
        name,
        slug,
        description,
        isActive
      }
    `)
    
    console.log(`Found ${categories.length} categories:`)
    categories.forEach(c => {
      console.log(`  ✅ ${c.name} - ${c.isActive ? 'Active' : 'Inactive'}`)
    })
    
    // Summary
    console.log('\n📊 SUMMARY:')
    console.log(`Total Products: ${products.length}`)
    console.log(`Total Jewelry: ${jewelry.length}`)
    console.log(`Total Brands: ${brands.length}`)
    console.log(`Total Categories: ${categories.length}`)
    
    // Check for missing references
    console.log('\n⚠️ MISSING REFERENCES:')
    const productsWithoutBrand = products.filter(p => !p.brand)
    const productsWithoutCategory = products.filter(p => !p.category)
    const jewelryWithoutBrand = jewelry.filter(j => !j.brand)
    const jewelryWithoutCategory = jewelry.filter(j => !j.category)
    
    if (productsWithoutBrand.length > 0) {
      console.log(`❌ ${productsWithoutBrand.length} products without brand`)
    }
    if (productsWithoutCategory.length > 0) {
      console.log(`❌ ${productsWithoutCategory.length} products without category`)
    }
    if (jewelryWithoutBrand.length > 0) {
      console.log(`❌ ${jewelryWithoutBrand.length} jewelry items without brand`)
    }
    if (jewelryWithoutCategory.length > 0) {
      console.log(`❌ ${jewelryWithoutCategory.length} jewelry items without category`)
    }
    
    if (productsWithoutBrand.length === 0 && productsWithoutCategory.length === 0 && 
        jewelryWithoutBrand.length === 0 && jewelryWithoutCategory.length === 0) {
      console.log('✅ All items have proper brand and category references!')
    }
    
    console.log('\n🎉 Data verification complete!')
    
  } catch (error) {
    console.error('❌ Error verifying data:', error)
  }
}

verifyDataDisplay()
