'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  Smartphone, 
  Building, 
  Lock, 
  Check,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer';
  name: string;
  icon: React.ReactNode;
  description: string;
  enabled: boolean;
}

export interface CardDetails {
  number: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  name: string;
  saveCard: boolean;
}

interface PaymentFormProps {
  selectedMethod: string;
  onMethodSelect: (methodId: string) => void;
  cardDetails: CardDetails;
  onCardDetailsChange: (details: Partial<CardDetails>) => void;
  onSubmit: () => void;
  isProcessing?: boolean;
}

const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'card',
    type: 'card',
    name: 'Credit/Debit Card',
    icon: <CreditCard className="h-5 w-5" />,
    description: 'Visa, Mastercard, American Express',
    enabled: true
  },
  {
    id: 'paypal',
    type: 'paypal',
    name: 'PayPal',
    icon: <div className="w-5 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">P</div>,
    description: 'Pay with your PayPal account',
    enabled: true
  },
  {
    id: 'apple_pay',
    type: 'apple_pay',
    name: 'Apple Pay',
    icon: <Smartphone className="h-5 w-5" />,
    description: 'Touch ID or Face ID',
    enabled: false
  },
  {
    id: 'google_pay',
    type: 'google_pay',
    name: 'Google Pay',
    icon: <Smartphone className="h-5 w-5" />,
    description: 'Pay with Google',
    enabled: false
  },
  {
    id: 'bank_transfer',
    type: 'bank_transfer',
    name: 'Bank Transfer',
    icon: <Building className="h-5 w-5" />,
    description: 'Direct bank transfer',
    enabled: true
  }
];

export function PaymentForm({
  selectedMethod,
  onMethodSelect,
  cardDetails,
  onCardDetailsChange,
  onSubmit,
  isProcessing = false
}: PaymentFormProps) {
  const [cardErrors, setCardErrors] = useState<Partial<Record<keyof CardDetails, string>>>({});

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const validateCard = () => {
    const errors: Partial<Record<keyof CardDetails, string>> = {};
    
    if (!cardDetails.number || cardDetails.number.replace(/\s/g, '').length < 13) {
      errors.number = 'Please enter a valid card number';
    }
    
    if (!cardDetails.name.trim()) {
      errors.name = 'Please enter the cardholder name';
    }
    
    if (!cardDetails.expiryMonth || !cardDetails.expiryYear) {
      errors.expiryMonth = 'Please enter expiry date';
    }
    
    if (!cardDetails.cvv || cardDetails.cvv.length < 3) {
      errors.cvv = 'Please enter a valid CVV';
    }
    
    setCardErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCardNumberChange = (value: string) => {
    const formatted = formatCardNumber(value);
    if (formatted.replace(/\s/g, '').length <= 16) {
      onCardDetailsChange({ number: formatted });
    }
  };

  const handleExpiryChange = (value: string) => {
    const formatted = formatExpiry(value);
    if (formatted.length <= 5) {
      const [month, year] = formatted.split('/');
      onCardDetailsChange({ 
        expiryMonth: month || '', 
        expiryYear: year || '' 
      });
    }
  };

  const handleCvvChange = (value: string) => {
    const v = value.replace(/[^0-9]/gi, '');
    if (v.length <= 4) {
      onCardDetailsChange({ cvv: v });
    }
  };

  const handleSubmit = () => {
    if (selectedMethod === 'card' && !validateCard()) {
      return;
    }
    onSubmit();
  };

  return (
    <div className="space-y-6">
      {/* Payment Method Selection */}
      <div>
        <h3 className="font-semibold text-gray-900 mb-4">Payment Method</h3>
        <div className="space-y-3">
          {PAYMENT_METHODS.map((method) => (
            <motion.div
              key={method.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "relative p-4 border-2 rounded-lg cursor-pointer transition-all",
                !method.enabled && "opacity-50 cursor-not-allowed",
                selectedMethod === method.id && method.enabled
                  ? "border-luxury-gold bg-luxury-gold/5"
                  : "border-gray-200 hover:border-gray-300"
              )}
              onClick={() => method.enabled && onMethodSelect(method.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full",
                    selectedMethod === method.id && method.enabled
                      ? "bg-luxury-gold text-luxury-black"
                      : "bg-gray-100 text-gray-600"
                  )}>
                    {method.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{method.name}</h4>
                    <p className="text-sm text-gray-600">{method.description}</p>
                  </div>
                </div>
                
                {selectedMethod === method.id && method.enabled && (
                  <div className="w-6 h-6 bg-luxury-gold rounded-full flex items-center justify-center">
                    <Check className="h-4 w-4 text-luxury-black" />
                  </div>
                )}
                
                {!method.enabled && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Coming Soon
                  </span>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Card Details Form */}
      {selectedMethod === 'card' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-4"
        >
          <h3 className="font-semibold text-gray-900">Card Details</h3>
          
          {/* Card Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Card Number *
            </label>
            <div className="relative">
              <input
                type="text"
                value={cardDetails.number}
                onChange={(e) => handleCardNumberChange(e.target.value)}
                placeholder="1234 5678 9012 3456"
                className={cn(
                  "w-full px-3 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent",
                  cardErrors.number ? "border-red-500" : "border-gray-300"
                )}
              />
              <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
            {cardErrors.number && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {cardErrors.number}
              </p>
            )}
          </div>

          {/* Cardholder Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cardholder Name *
            </label>
            <input
              type="text"
              value={cardDetails.name}
              onChange={(e) => onCardDetailsChange({ name: e.target.value })}
              placeholder="John Doe"
              className={cn(
                "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent",
                cardErrors.name ? "border-red-500" : "border-gray-300"
              )}
            />
            {cardErrors.name && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {cardErrors.name}
              </p>
            )}
          </div>

          {/* Expiry and CVV */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiry Date *
              </label>
              <input
                type="text"
                value={cardDetails.expiryMonth && cardDetails.expiryYear ? `${cardDetails.expiryMonth}/${cardDetails.expiryYear}` : ''}
                onChange={(e) => handleExpiryChange(e.target.value)}
                placeholder="MM/YY"
                className={cn(
                  "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent",
                  cardErrors.expiryMonth ? "border-red-500" : "border-gray-300"
                )}
              />
              {cardErrors.expiryMonth && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {cardErrors.expiryMonth}
                </p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CVV *
              </label>
              <input
                type="text"
                value={cardDetails.cvv}
                onChange={(e) => handleCvvChange(e.target.value)}
                placeholder="123"
                className={cn(
                  "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent",
                  cardErrors.cvv ? "border-red-500" : "border-gray-300"
                )}
              />
              {cardErrors.cvv && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {cardErrors.cvv}
                </p>
              )}
            </div>
          </div>

          {/* Save Card */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="saveCard"
              checked={cardDetails.saveCard}
              onChange={(e) => onCardDetailsChange({ saveCard: e.target.checked })}
              className="rounded border-gray-300 text-luxury-gold focus:ring-luxury-gold"
            />
            <label htmlFor="saveCard" className="ml-2 text-sm text-gray-700">
              Save this card for future purchases
            </label>
          </div>
        </motion.div>
      )}

      {/* PayPal */}
      {selectedMethod === 'paypal' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
              P
            </div>
            <div>
              <h4 className="font-medium text-blue-900">PayPal Payment</h4>
              <p className="text-sm text-blue-700">
                You will be redirected to PayPal to complete your payment securely.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Bank Transfer */}
      {selectedMethod === 'bank_transfer' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-gray-50 border border-gray-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-3">
            <Building className="h-8 w-8 text-gray-600" />
            <div>
              <h4 className="font-medium text-gray-900">Bank Transfer</h4>
              <p className="text-sm text-gray-600">
                Bank transfer details will be provided after order confirmation.
                Payment must be completed within 3 business days.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Security Notice */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <Lock className="h-5 w-5 text-green-600" />
          <div>
            <h4 className="font-medium text-green-900">Secure Payment</h4>
            <p className="text-sm text-green-700">
              Your payment information is encrypted and secure. We never store your card details.
            </p>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <Button
        onClick={handleSubmit}
        disabled={isProcessing || !selectedMethod}
        className="w-full"
        variant="luxury"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="w-4 h-4 border-2 border-luxury-black border-t-transparent rounded-full animate-spin mr-2" />
            Processing Payment...
          </>
        ) : (
          <>
            <Lock className="h-4 w-4 mr-2" />
            Complete Payment
          </>
        )}
      </Button>
    </div>
  );
}
