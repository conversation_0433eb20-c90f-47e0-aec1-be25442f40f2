'use client'

import { useState } from 'react'
import Image from 'next/image'
import { urlFor } from '@/lib/sanity/client'
import { cn } from '@/lib/utils'

interface SanityImageProps {
  image: any
  alt?: string
  width?: number
  height?: number
  className?: string
  fill?: boolean
  sizes?: string
  priority?: boolean
  quality?: number
  crop?: string
  hotspot?: boolean
  blur?: boolean
  placeholder?: 'blur' | 'empty'
  onLoad?: () => void
  onError?: () => void
}

export function SanityImage({
  image,
  alt,
  width,
  height,
  className,
  fill = false,
  sizes,
  priority = false,
  quality = 85,
  crop,
  hotspot = true,
  blur = false,
  placeholder = 'blur',
  onLoad,
  onError
}: SanityImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  if (!image?.asset) {
    return (
      <div className={cn(
        "bg-gray-200 flex items-center justify-center text-gray-400",
        className
      )}>
        <span className="text-sm">No image</span>
      </div>
    )
  }

  // Build Sanity image URL with transformations
  let imageBuilder = urlFor(image)
    .quality(quality)
    .format('webp')

  if (width && height && !fill) {
    imageBuilder = imageBuilder.width(width).height(height)
  }

  if (crop) {
    imageBuilder = imageBuilder.crop(crop as any)
  }

  if (hotspot && image.hotspot) {
    imageBuilder = imageBuilder.crop('focalpoint')
  }

  if (blur) {
    imageBuilder = imageBuilder.blur(20)
  }

  const imageUrl = imageBuilder.url()
  const blurDataUrl = blur ? 
    urlFor(image).width(20).height(20).blur(50).quality(20).url() : 
    undefined

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    setIsLoading(false)
    onError?.()
  }

  if (hasError) {
    return (
      <div className={cn(
        "bg-gray-200 flex items-center justify-center text-gray-400",
        className
      )}>
        <span className="text-sm">Failed to load image</span>
      </div>
    )
  }

  const imageProps = {
    src: imageUrl,
    alt: alt || image.alt || '',
    className: cn(
      'transition-opacity duration-300',
      isLoading && 'opacity-0',
      className
    ),
    onLoad: handleLoad,
    onError: handleError,
    priority,
    quality,
    sizes,
    ...(blurDataUrl && {
      placeholder: 'blur' as const,
      blurDataURL: blurDataUrl
    })
  }

  if (fill) {
    return (
      <Image
        {...imageProps}
        fill
      />
    )
  }

  if (width && height) {
    return (
      <Image
        {...imageProps}
        width={width}
        height={height}
      />
    )
  }

  // Fallback to fill if no dimensions provided
  return (
    <Image
      {...imageProps}
      fill
    />
  )
}

// Specialized components for common use cases
export function ProductImage({ 
  image, 
  alt, 
  className,
  priority = false 
}: {
  image: any
  alt?: string
  className?: string
  priority?: boolean
}) {
  return (
    <SanityImage
      image={image}
      alt={alt}
      className={className}
      fill
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      priority={priority}
      hotspot
    />
  )
}

export function HeroImage({ 
  image, 
  alt, 
  className 
}: {
  image: any
  alt?: string
  className?: string
}) {
  return (
    <SanityImage
      image={image}
      alt={alt}
      className={className}
      fill
      sizes="100vw"
      priority
      quality={95}
      hotspot
    />
  )
}

export function ThumbnailImage({ 
  image, 
  alt, 
  size = 150,
  className 
}: {
  image: any
  alt?: string
  size?: number
  className?: string
}) {
  return (
    <SanityImage
      image={image}
      alt={alt}
      width={size}
      height={size}
      className={className}
      quality={80}
      crop="center"
    />
  )
}

export function CardImage({ 
  image, 
  alt, 
  className,
  aspectRatio = 'square' 
}: {
  image: any
  alt?: string
  className?: string
  aspectRatio?: 'square' | 'landscape' | 'portrait'
}) {
  const aspectClasses = {
    square: 'aspect-square',
    landscape: 'aspect-[4/3]',
    portrait: 'aspect-[3/4]'
  }

  return (
    <div className={cn('relative overflow-hidden', aspectClasses[aspectRatio], className)}>
      <SanityImage
        image={image}
        alt={alt}
        fill
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
        hotspot
      />
    </div>
  )
}

// Gallery component for multiple images
export function ImageGallery({ 
  images, 
  className 
}: {
  images: any[]
  className?: string
}) {
  if (!images || images.length === 0) {
    return null
  }

  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4', className)}>
      {images.map((image, index) => (
        <CardImage
          key={image._key || index}
          image={image}
          alt={image.alt}
          className="rounded-lg overflow-hidden"
        />
      ))}
    </div>
  )
}

// Responsive image component with multiple breakpoints
export function ResponsiveImage({
  image,
  alt,
  className,
  breakpoints = {
    mobile: 400,
    tablet: 800,
    desktop: 1200
  }
}: {
  image: any
  alt?: string
  className?: string
  breakpoints?: {
    mobile: number
    tablet: number
    desktop: number
  }
}) {
  if (!image?.asset) return null

  const srcSet = [
    `${urlFor(image).width(breakpoints.mobile).quality(85).url()} ${breakpoints.mobile}w`,
    `${urlFor(image).width(breakpoints.tablet).quality(85).url()} ${breakpoints.tablet}w`,
    `${urlFor(image).width(breakpoints.desktop).quality(85).url()} ${breakpoints.desktop}w`
  ].join(', ')

  const sizes = `(max-width: 768px) ${breakpoints.mobile}px, (max-width: 1200px) ${breakpoints.tablet}px, ${breakpoints.desktop}px`

  return (
    <img
      src={urlFor(image).width(breakpoints.desktop).quality(85).url()}
      srcSet={srcSet}
      sizes={sizes}
      alt={alt || image.alt || ''}
      className={className}
      loading="lazy"
    />
  )
}
