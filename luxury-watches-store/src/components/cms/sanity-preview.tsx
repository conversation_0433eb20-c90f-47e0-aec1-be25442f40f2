'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Eye, EyeOff, RefreshCw } from 'lucide-react'

interface SanityPreviewProps {
  isEnabled?: boolean
  onToggle?: (enabled: boolean) => void
}

export function SanityPreview({ isEnabled = false, onToggle }: SanityPreviewProps) {
  const [previewMode, setPreviewMode] = useState(isEnabled)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setPreviewMode(isEnabled)
  }, [isEnabled])

  const togglePreview = async () => {
    setIsLoading(true)
    
    try {
      if (previewMode) {
        // Exit preview mode
        await fetch('/api/preview/exit', { method: 'POST' })
        setPreviewMode(false)
        onToggle?.(false)
      } else {
        // Enter preview mode
        await fetch('/api/preview/enable', { method: 'POST' })
        setPreviewMode(true)
        onToggle?.(true)
      }
      
      // Refresh the page to apply changes
      router.refresh()
    } catch (error) {
      console.error('Failed to toggle preview mode:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const refreshPreview = () => {
    setIsLoading(true)
    router.refresh()
    setTimeout(() => setIsLoading(false), 1000)
  }

  if (!previewMode) {
    return (
      <button
        onClick={togglePreview}
        disabled={isLoading}
        className="fixed bottom-4 right-4 z-50 bg-luxury-black text-luxury-cream px-4 py-2 rounded-lg shadow-lg hover:bg-luxury-charcoal transition-colors flex items-center space-x-2"
      >
        <EyeOff className="w-4 h-4" />
        <span>Enable Preview</span>
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-luxury-gold text-luxury-black px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
      <Eye className="w-4 h-4" />
      <span className="font-medium">Preview Mode</span>
      
      <div className="flex items-center space-x-1 ml-2">
        <button
          onClick={refreshPreview}
          disabled={isLoading}
          className="p-1 hover:bg-luxury-gold-dark rounded transition-colors"
          title="Refresh preview"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
        
        <button
          onClick={togglePreview}
          disabled={isLoading}
          className="p-1 hover:bg-luxury-gold-dark rounded transition-colors"
          title="Exit preview"
        >
          <EyeOff className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// Preview banner component
export function PreviewBanner() {
  return (
    <div className="bg-luxury-gold text-luxury-black px-4 py-2 text-center text-sm font-medium">
      <div className="flex items-center justify-center space-x-2">
        <Eye className="w-4 h-4" />
        <span>Preview Mode Active - You are viewing draft content</span>
        <Link
          href="/api/preview/exit"
          className="underline hover:no-underline ml-2"
        >
          Exit Preview
        </Link>
      </div>
    </div>
  )
}

// Hook for preview state
export function usePreview() {
  const [isPreview, setIsPreview] = useState(false)

  useEffect(() => {
    // Check if we're in preview mode
    const checkPreviewMode = () => {
      const isPreviewMode = document.cookie.includes('__prerender_bypass') || 
                           document.cookie.includes('__next_preview_data')
      setIsPreview(isPreviewMode)
    }

    checkPreviewMode()
    
    // Listen for preview mode changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        checkPreviewMode()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return isPreview
}

// Preview wrapper component
export function PreviewWrapper({ 
  children, 
  showBanner = true 
}: { 
  children: React.ReactNode
  showBanner?: boolean 
}) {
  const isPreview = usePreview()

  return (
    <>
      {isPreview && showBanner && <PreviewBanner />}
      {children}
      {isPreview && <SanityPreview isEnabled={true} />}
    </>
  )
}
