'use client'

import { PortableText as BasePortableText, PortableTextComponents } from '@portabletext/react'
import Image from 'next/image'
import Link from 'next/link'
import { urlFor } from '@/lib/sanity/client'
import { SanityImage } from './sanity-image'
import { cn } from '@/lib/utils'

const components: PortableTextComponents = {
  types: {
    image: ({ value }) => {
      if (!value?.asset) return null
      
      return (
        <figure className="my-8">
          <div className="relative aspect-video rounded-lg overflow-hidden bg-gray-100">
            <SanityImage
              image={value}
              alt={value.alt}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              className="object-cover"
            />
          </div>
          {value.caption && (
            <figcaption className="text-sm text-gray-600 mt-3 text-center italic">
              {value.caption}
            </figcaption>
          )}
        </figure>
      )
    },
    callout: ({ value }) => (
      <div className="my-6 p-6 bg-luxury-gold/10 border-l-4 border-luxury-gold rounded-r-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0 w-6 h-6 bg-luxury-gold rounded-full flex items-center justify-center mr-3 mt-0.5">
            <span className="text-luxury-black text-sm font-bold">!</span>
          </div>
          <div className="text-gray-800">
            <BasePortableText value={value.content} components={components} />
          </div>
        </div>
      </div>
    ),
    productReference: ({ value }) => {
      if (!value?.product) return null
      
      return (
        <div className="my-8 p-6 bg-gray-50 rounded-lg border">
          <h4 className="text-lg font-semibold text-luxury-black mb-2">
            Featured Product
          </h4>
          <div className="flex items-center space-x-4">
            {value.product.images?.[0] && (
              <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                <SanityImage
                  image={value.product.images[0]}
                  alt={value.product.name}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div>
              <h5 className="font-medium text-luxury-black">
                {value.product.name}
              </h5>
              <p className="text-sm text-gray-600">
                {value.product.brand?.name}
              </p>
              <Link
                href={`/catalog/${value.product.slug?.current}`}
                className="text-luxury-gold hover:text-luxury-gold-dark text-sm font-medium"
              >
                View Product →
              </Link>
            </div>
          </div>
        </div>
      )
    },
    gallery: ({ value }) => {
      if (!value?.images || value.images.length === 0) return null
      
      return (
        <div className="my-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {value.images.map((image: any, index: number) => (
              <div key={image._key || index} className="relative aspect-square rounded-lg overflow-hidden">
                <SanityImage
                  image={image}
                  alt={image.alt}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
          {value.caption && (
            <p className="text-sm text-gray-600 mt-3 text-center italic">
              {value.caption}
            </p>
          )}
        </div>
      )
    }
  },
  marks: {
    link: ({ children, value }) => {
      const rel = !value.href.startsWith('/') ? 'noreferrer noopener' : undefined
      const isExternal = !value.href.startsWith('/')
      
      return (
        <Link
          href={value.href}
          rel={rel}
          target={isExternal ? '_blank' : undefined}
          className="text-luxury-gold hover:text-luxury-gold-dark underline decoration-luxury-gold/30 hover:decoration-luxury-gold transition-colors"
        >
          {children}
          {isExternal && (
            <span className="inline-block ml-1 text-xs">↗</span>
          )}
        </Link>
      )
    },
    strong: ({ children }) => (
      <strong className="font-bold text-luxury-black">{children}</strong>
    ),
    em: ({ children }) => (
      <em className="italic text-gray-700">{children}</em>
    ),
    highlight: ({ children }) => (
      <mark className="bg-luxury-gold/20 px-1 rounded">{children}</mark>
    )
  },
  block: {
    h1: ({ children }) => (
      <h1 className="text-4xl md:text-5xl font-bold text-luxury-black mb-6 leading-tight">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-3xl md:text-4xl font-bold text-luxury-black mb-5 leading-tight">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-2xl md:text-3xl font-bold text-luxury-black mb-4 leading-tight">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-xl md:text-2xl font-bold text-luxury-black mb-3 leading-tight">
        {children}
      </h4>
    ),
    normal: ({ children }) => (
      <p className="text-gray-700 mb-4 leading-relaxed text-lg">
        {children}
      </p>
    ),
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-luxury-gold pl-6 my-8 italic text-gray-600 text-lg bg-luxury-gold/5 py-4 rounded-r-lg">
        {children}
      </blockquote>
    ),
    large: ({ children }) => (
      <p className="text-xl text-gray-700 mb-6 leading-relaxed font-medium">
        {children}
      </p>
    )
  },
  list: {
    bullet: ({ children }) => (
      <ul className="list-none mb-6 space-y-3 text-gray-700">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="list-decimal list-inside mb-6 space-y-3 text-gray-700 ml-4">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="flex items-start">
        <span className="w-2 h-2 bg-luxury-gold rounded-full mt-3 mr-3 flex-shrink-0"></span>
        <span>{children}</span>
      </li>
    ),
    number: ({ children }) => <li className="ml-2">{children}</li>,
  },
}

interface PortableTextProps {
  value: any
  className?: string
  variant?: 'default' | 'compact' | 'large'
}

export function PortableText({ 
  value, 
  className = '', 
  variant = 'default' 
}: PortableTextProps) {
  if (!value) return null

  const variantClasses = {
    default: 'prose prose-lg max-w-none',
    compact: 'prose max-w-none text-sm',
    large: 'prose prose-xl max-w-none'
  }

  return (
    <div className={cn(variantClasses[variant], className)}>
      <BasePortableText value={value} components={components} />
    </div>
  )
}

// Specialized components for different content types
export function BlogContent({ value, className }: { value: any, className?: string }) {
  return (
    <PortableText 
      value={value} 
      variant="large"
      className={cn('blog-content', className)}
    />
  )
}

export function ProductDescription({ value, className }: { value: any, className?: string }) {
  return (
    <PortableText 
      value={value} 
      variant="default"
      className={cn('product-description', className)}
    />
  )
}

export function CompactContent({ value, className }: { value: any, className?: string }) {
  return (
    <PortableText 
      value={value} 
      variant="compact"
      className={cn('compact-content', className)}
    />
  )
}
