'use client'

import { useReportWebVitals } from 'next/web-vitals'

export function WebVitals() {
  useReportWebVitals((metric) => {
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Web Vital:', metric)
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
      // Send to Google Analytics
      if (window.gtag) {
        window.gtag('event', metric.name, {
          custom_map: { metric_id: 'custom_metric' },
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          event_category: 'Web Vitals',
          event_label: metric.id,
          non_interaction: true,
        })
      }

      // Send to custom analytics endpoint
      fetch('/api/analytics/web-vitals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: metric.name,
          value: metric.value,
          id: metric.id,
          label: metric.label,
          delta: metric.delta,
          rating: metric.rating,
          navigationType: metric.navigationType,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      }).catch((error) => {
        console.error('Failed to send web vitals:', error)
      })
    }
  })

  return null
}

// Performance monitoring utilities
export const performanceUtils = {
  // Measure component render time
  measureRender: (componentName: string, fn: () => void) => {
    const start = performance.now()
    fn()
    const end = performance.now()
    console.log(`${componentName} render time: ${end - start}ms`)
  },

  // Measure API call time
  measureApiCall: async (apiName: string, apiCall: () => Promise<any>) => {
    const start = performance.now()
    try {
      const result = await apiCall()
      const end = performance.now()
      console.log(`${apiName} API call time: ${end - start}ms`)
      return result
    } catch (error) {
      const end = performance.now()
      console.error(`${apiName} API call failed after ${end - start}ms:`, error)
      throw error
    }
  },

  // Log navigation timing
  logNavigationTiming: () => {
    if (typeof window === 'undefined') return

    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      console.log('Navigation Timing:', {
        'DNS Lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
        'TCP Connection': navigation.connectEnd - navigation.connectStart,
        'Request': navigation.responseStart - navigation.requestStart,
        'Response': navigation.responseEnd - navigation.responseStart,
        'DOM Processing': navigation.domContentLoadedEventStart - navigation.responseEnd,
        'Load Complete': navigation.loadEventEnd - navigation.loadEventStart,
        'Total': navigation.loadEventEnd - navigation.navigationStart,
      })
    })
  },

  // Monitor resource loading
  monitorResources: () => {
    if (typeof window === 'undefined') return

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 1000) { // Log slow resources (>1s)
          console.warn('Slow resource:', {
            name: entry.name,
            duration: entry.duration,
            size: (entry as any).transferSize,
          })
        }
      })
    })

    observer.observe({ entryTypes: ['resource'] })
  },
}
