'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { 
  Star, 
  Shield, 
  Truck, 
  Award, 
  Clock, 
  Users, 
  ArrowRight,
  Quote,
  CheckCircle
} from 'lucide-react'

// Trust Badges Component
export function TrustBadges() {
  const badges = [
    {
      icon: Shield,
      title: "Authenticity Guaranteed",
      description: "Every piece certified authentic"
    },
    {
      icon: Truck,
      title: "Free Worldwide Shipping",
      description: "Complimentary delivery on all orders"
    },
    {
      icon: Award,
      title: "Award-Winning Service",
      description: "Recognized excellence in luxury retail"
    },
    {
      icon: Clock,
      title: "Lifetime Support",
      description: "Expert care for your timepieces"
    }
  ]

  return (
    <section className="py-16 bg-luxury-cream/30">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {badges.map((badge, index) => (
            <motion.div
              key={badge.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-luxury-gold/10 rounded-full mb-4 group-hover:bg-luxury-gold/20 transition-colors duration-300">
                <badge.icon className="w-8 h-8 text-luxury-gold" />
              </div>
              <h3 className="text-lg font-bold text-luxury-black mb-2">{badge.title}</h3>
              <p className="text-gray-600">{badge.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Customer Testimonials Component
export function CustomerTestimonials() {
  const testimonials = [
    {
      id: 1,
      name: "James Morrison",
      title: "Watch Collector",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Exceptional service and an incredible selection. I've purchased three timepieces from Atlas Luxury, and each experience has been flawless. The authenticity and quality are unmatched."
    },
    {
      id: 2,
      name: "Sarah Chen",
      title: "Luxury Enthusiast",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "The expertise and passion of the Atlas Luxury team is evident in every interaction. They helped me find the perfect anniversary gift – a stunning Cartier piece that exceeded all expectations."
    },
    {
      id: 3,
      name: "Michael Rodriguez",
      title: "Investment Advisor",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "As someone who appreciates both luxury and investment value, Atlas Luxury delivers on both fronts. Their curation is impeccable, and their market knowledge is invaluable."
    }
  ]

  return (
    <section className="py-20 bg-luxury-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-luxury-black mb-4">
            What Our Clients Say
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover why discerning collectors choose Atlas Luxury for their most precious acquisitions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-shadow duration-300 relative"
            >
              <Quote className="w-8 h-8 text-luxury-gold/30 mb-4" />
              
              <p className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.text}"
              </p>

              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-luxury-gold fill-current" />
                ))}
              </div>

              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    width={48}
                    height={48}
                    className="object-cover"
                  />
                </div>
                <div>
                  <h4 className="font-bold text-luxury-black">{testimonial.name}</h4>
                  <p className="text-sm text-gray-600">{testimonial.title}</p>
                </div>
              </div>

              <div className="absolute top-6 right-6">
                <div className="w-2 h-2 bg-luxury-gold rounded-full"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Call to Action Component
export function CallToAction() {
  return (
    <section className="py-20 bg-gradient-to-r from-luxury-black to-luxury-charcoal text-luxury-cream">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Begin Your Luxury Journey
            </h2>
            <p className="text-xl text-luxury-cream/80 mb-8 leading-relaxed">
              Join thousands of discerning collectors who trust Atlas Luxury for their most 
              precious timepieces. Experience the difference that expertise, authenticity, 
              and exceptional service make.
            </p>

            <div className="space-y-4 mb-8">
              {[
                "Curated selection from the world's finest brands",
                "Expert authentication and certification",
                "Personalized consultation and service",
                "Lifetime support and maintenance guidance"
              ].map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className="flex items-center"
                >
                  <CheckCircle className="w-6 h-6 text-luxury-gold mr-3 flex-shrink-0" />
                  <span className="text-luxury-cream/90">{feature}</span>
                </motion.div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/catalog"
                className="group inline-flex items-center justify-center px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-light transition-all duration-300 transform hover:scale-105"
              >
                Explore Collection
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                href="/consultation"
                className="group inline-flex items-center justify-center px-8 py-4 border-2 border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-all duration-300"
              >
                Book Consultation
              </Link>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=800&h=600&fit=crop&crop=center"
                alt="Luxury watch collection"
                fill
                className="object-cover"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-luxury-black/40 via-transparent to-transparent" />
              
              {/* Floating Stats */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1, duration: 0.6 }}
                viewport={{ once: true }}
                className="absolute top-6 left-6 bg-luxury-cream/90 backdrop-blur-sm rounded-lg p-4"
              >
                <div className="text-2xl font-bold text-luxury-black">50K+</div>
                <div className="text-sm text-gray-600">Happy Customers</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2, duration: 0.6 }}
                viewport={{ once: true }}
                className="absolute bottom-6 right-6 bg-luxury-gold/90 backdrop-blur-sm rounded-lg p-4"
              >
                <div className="text-2xl font-bold text-luxury-black">25+</div>
                <div className="text-sm text-luxury-black/70">Years Experience</div>
              </motion.div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 border border-luxury-gold/30 rounded-full" />
            <div className="absolute -bottom-4 -left-4 w-16 h-16 border border-luxury-gold/30 rounded-full" />
          </motion.div>
        </div>
      </div>
    </section>
  )
}

// Statistics Component
export function Statistics() {
  const stats = [
    { icon: Users, number: "50,000+", label: "Satisfied Customers" },
    { icon: Award, number: "500+", label: "Luxury Brands" },
    { icon: Clock, number: "25+", label: "Years Experience" },
    { icon: Shield, number: "100%", label: "Authenticity Guaranteed" }
  ]

  return (
    <section className="py-16 bg-luxury-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-luxury-gold/10 rounded-full mb-4">
                <stat.icon className="w-8 h-8 text-luxury-gold" />
              </div>
              <div className="text-3xl font-bold text-luxury-black mb-2">{stat.number}</div>
              <div className="text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
