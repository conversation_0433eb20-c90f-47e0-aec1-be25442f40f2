'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight, Award, Clock, Users, Globe } from 'lucide-react'

interface BrandStoryProps {
  title?: string
  subtitle?: string
  description?: string
  stats?: Array<{
    icon: any
    number: string
    label: string
  }>
  brands?: Array<{
    id: string
    name: string
    logo: string
    description: string
    founded: number
    country: string
    heritage: string
  }>
}

export function BrandStory({ 
  title = "Heritage & Craftsmanship",
  subtitle = "Discover the stories behind the world's most prestigious luxury brands",
  description = "Each timepiece in our collection represents generations of master craftsmanship, innovation, and unwavering dedication to perfection. From Swiss precision to Italian elegance, explore the rich heritage that defines luxury.",
  stats = [
    { icon: Clock, number: "250+", label: "Years Combined Heritage" },
    { icon: Award, number: "50+", label: "Master Craftsmen" },
    { icon: Users, number: "100K+", label: "Satisfied Collectors" },
    { icon: Globe, number: "25+", label: "Countries Served" }
  ],
  brands = []
}: BrandStoryProps) {
  return (
    <section className="py-20 bg-gradient-to-br from-luxury-charcoal to-luxury-black text-luxury-cream">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            {title}
          </h2>
          <p className="text-xl text-luxury-cream/80 max-w-3xl mx-auto mb-8">
            {subtitle}
          </p>
          <p className="text-lg text-luxury-cream/70 max-w-4xl mx-auto leading-relaxed">
            {description}
          </p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-luxury-gold/10 rounded-full mb-4 group-hover:bg-luxury-gold/20 transition-colors duration-300">
                <stat.icon className="w-8 h-8 text-luxury-gold" />
              </div>
              <div className="text-3xl font-bold text-luxury-gold mb-2">{stat.number}</div>
              <div className="text-luxury-cream/70">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Brand Showcase */}
        {brands.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <h3 className="text-3xl font-bold text-center mb-12">Featured Brands</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {brands.slice(0, 6).map((brand, index) => (
                <motion.div
                  key={brand.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className="group bg-luxury-black/50 backdrop-blur-sm rounded-2xl p-6 border border-luxury-gold/20 hover:border-luxury-gold/40 transition-all duration-300"
                >
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-luxury-cream rounded-lg flex items-center justify-center mr-4">
                      <Image
                        src={brand.logo}
                        alt={`${brand.name} logo`}
                        width={32}
                        height={32}
                        className="object-contain"
                      />
                    </div>
                    <div>
                      <h4 className="text-xl font-bold text-luxury-cream">{brand.name}</h4>
                      <p className="text-sm text-luxury-gold">Est. {brand.founded} • {brand.country}</p>
                    </div>
                  </div>
                  <p className="text-luxury-cream/70 text-sm mb-4 line-clamp-3">
                    {brand.heritage}
                  </p>
                  <Link
                    href={`/brands/${brand.id}`}
                    className="inline-flex items-center text-luxury-gold hover:text-luxury-gold-light transition-colors font-medium text-sm"
                  >
                    Learn More
                    <ArrowRight className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Heritage Story */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.8 }}
          viewport={{ once: true }}
          className="grid lg:grid-cols-2 gap-12 items-center"
        >
          {/* Content */}
          <div>
            <h3 className="text-3xl font-bold mb-6">A Legacy of Excellence</h3>
            <div className="space-y-4 text-luxury-cream/80 leading-relaxed">
              <p>
                For over two centuries, the world's finest watchmakers have pushed the boundaries 
                of precision, artistry, and innovation. Each timepiece represents not just a tool 
                for measuring time, but a masterpiece of human ingenuity.
              </p>
              <p>
                From the intricate complications of Swiss manufactures to the bold designs of 
                contemporary masters, our collection celebrates the full spectrum of horological 
                excellence. Every watch tells a story of dedication, passion, and the relentless 
                pursuit of perfection.
              </p>
              <p>
                When you choose a timepiece from our collection, you're not just acquiring a watch – 
                you're becoming part of a legacy that spans generations, connecting you to the 
                master craftsmen who poured their souls into creating these mechanical marvels.
              </p>
            </div>
            
            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <Link
                href="/brands"
                className="group inline-flex items-center px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-light transition-all duration-300"
              >
                Explore All Brands
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                href="/heritage"
                className="group inline-flex items-center px-6 py-3 border border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-all duration-300"
              >
                Our Heritage Story
              </Link>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              viewport={{ once: true }}
              className="relative aspect-[4/3] rounded-2xl overflow-hidden"
            >
              <Image
                src="https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=800&h=600&fit=crop&crop=center"
                alt="Master craftsman at work"
                fill
                className="object-cover"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-luxury-black/60 via-transparent to-transparent" />
              
              {/* Caption */}
              <div className="absolute bottom-6 left-6 right-6">
                <p className="text-luxury-cream text-sm font-medium">
                  Master craftsmen dedicate years to perfecting their art, ensuring each timepiece 
                  meets the highest standards of excellence.
                </p>
              </div>
            </motion.div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 border border-luxury-gold/30 rounded-full" />
            <div className="absolute -bottom-4 -left-4 w-16 h-16 border border-luxury-gold/30 rounded-full" />
          </div>
        </motion.div>
      </div>
    </section>
  )
}
