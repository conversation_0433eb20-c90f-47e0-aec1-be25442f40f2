'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight, Star, Shield, Award } from 'lucide-react'

export function HeroSection() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-luxury-black via-luxury-charcoal to-luxury-black overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/images/luxury-pattern.svg')] bg-repeat opacity-20"></div>
      </div>
      
      {/* Hero Content */}
      <div className="relative z-10 container mx-auto px-4 pt-20 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center lg:text-left"
          >
            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="flex items-center justify-center lg:justify-start space-x-6 mb-6"
            >
              <div className="flex items-center space-x-2">
                <Star className="w-5 h-5 text-luxury-gold fill-current" />
                <span className="text-luxury-cream text-sm">5.0 Rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="w-5 h-5 text-luxury-gold" />
                <span className="text-luxury-cream text-sm">Certified Authentic</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5 text-luxury-gold" />
                <span className="text-luxury-cream text-sm">Award Winning</span>
              </div>
            </motion.div>

            {/* Main Headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-4xl md:text-6xl lg:text-7xl font-bold text-luxury-cream mb-6 leading-tight"
            >
              Timeless
              <span className="block text-luxury-gold">Luxury</span>
              <span className="block">Redefined</span>
            </motion.h1>

            {/* Subheadline */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-xl md:text-2xl text-luxury-cream/80 mb-8 max-w-2xl mx-auto lg:mx-0"
            >
              Discover the world's most exquisite timepieces and jewelry. 
              Each piece tells a story of craftsmanship, heritage, and uncompromising quality.
            </motion.p>

            {/* Value Proposition */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-10"
            >
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-luxury-gold mb-1">500+</div>
                <div className="text-luxury-cream/70 text-sm">Luxury Brands</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-luxury-gold mb-1">50K+</div>
                <div className="text-luxury-cream/70 text-sm">Happy Customers</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-luxury-gold mb-1">25+</div>
                <div className="text-luxury-cream/70 text-sm">Years Experience</div>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
            >
              <Link
                href="/catalog"
                className="group inline-flex items-center justify-center px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-light transition-all duration-300 transform hover:scale-105"
              >
                Explore Collection
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                href="/brands"
                className="group inline-flex items-center justify-center px-8 py-4 border-2 border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-all duration-300"
              >
                Discover Brands
              </Link>
            </motion.div>

            {/* Social Proof */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2, duration: 0.8 }}
              className="mt-12 pt-8 border-t border-luxury-cream/20"
            >
              <p className="text-luxury-cream/60 text-sm mb-4">Trusted by luxury enthusiasts worldwide</p>
              <div className="flex items-center justify-center lg:justify-start space-x-8 opacity-60">
                <div className="text-luxury-cream text-xs">ROLEX</div>
                <div className="text-luxury-cream text-xs">PATEK PHILIPPE</div>
                <div className="text-luxury-cream text-xs">AUDEMARS PIGUET</div>
                <div className="text-luxury-cream text-xs">CARTIER</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Hero Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
            className="relative"
          >
            <div className="relative z-10">
              {/* Main Hero Image */}
              <div className="relative aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-luxury-gold/20 to-luxury-cream/10 backdrop-blur-sm border border-luxury-gold/30">
                <Image
                  src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=800&fit=crop&crop=center"
                  alt="Luxury timepiece collection"
                  fill
                  className="object-cover"
                  priority
                />
                
                {/* Floating Elements */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.5, duration: 0.6 }}
                  className="absolute top-6 right-6 bg-luxury-black/80 backdrop-blur-sm rounded-lg p-4 text-luxury-cream"
                >
                  <div className="text-sm font-medium">Featured</div>
                  <div className="text-xs opacity-70">Rolex Submariner</div>
                  <div className="text-luxury-gold font-bold">$12,500</div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.8, duration: 0.6 }}
                  className="absolute bottom-6 left-6 bg-luxury-gold/90 backdrop-blur-sm rounded-lg p-4 text-luxury-black"
                >
                  <div className="text-sm font-medium">New Arrival</div>
                  <div className="text-xs opacity-70">Limited Edition</div>
                </motion.div>
              </div>

              {/* Decorative Elements */}
              <motion.div
                initial={{ opacity: 0, rotate: -180 }}
                animate={{ opacity: 0.3, rotate: 0 }}
                transition={{ delay: 2, duration: 1 }}
                className="absolute -top-8 -right-8 w-32 h-32 border border-luxury-gold/30 rounded-full"
              />
              <motion.div
                initial={{ opacity: 0, rotate: 180 }}
                animate={{ opacity: 0.2, rotate: 0 }}
                transition={{ delay: 2.2, duration: 1 }}
                className="absolute -bottom-8 -left-8 w-24 h-24 border border-luxury-gold/30 rounded-full"
              />
            </div>

            {/* Background Glow */}
            <div className="absolute inset-0 bg-gradient-to-r from-luxury-gold/20 to-luxury-cream/20 rounded-2xl blur-3xl transform scale-110" />
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2.5, duration: 0.8 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
          className="w-6 h-10 border-2 border-luxury-gold/50 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ repeat: Infinity, duration: 2 }}
            className="w-1 h-3 bg-luxury-gold rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  )
}
