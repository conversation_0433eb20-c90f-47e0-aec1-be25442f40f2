'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowR<PERSON>, Clock, Gem, Star, Eye } from 'lucide-react'

interface ProductShowcaseProps {
  title: string
  subtitle: string
  products: Array<{
    id: string
    name: string
    brand: string
    price: number
    image: string
    category: string
    rating?: number
    isNew?: boolean
  }>
  viewAllLink: string
  type: 'watches' | 'jewelry'
}

export function ProductShowcase({ title, subtitle, products, viewAllLink, type }: ProductShowcaseProps) {
  const icon = type === 'watches' ? Clock : Gem
  const IconComponent = icon

  return (
    <section className="py-20 bg-luxury-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-luxury-gold/10 rounded-full mb-6">
            <IconComponent className="w-8 h-8 text-luxury-gold" />
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-luxury-black mb-4">
            {title}
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden"
            >
              {/* Product Image */}
              <div className="relative aspect-square overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-700"
                />
                
                {/* Badges */}
                <div className="absolute top-4 left-4 flex flex-col gap-2">
                  {product.isNew && (
                    <span className="bg-luxury-gold text-luxury-black text-xs font-bold px-3 py-1 rounded-full">
                      NEW
                    </span>
                  )}
                  {product.rating && product.rating >= 4.5 && (
                    <span className="bg-luxury-black text-luxury-cream text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                      <Star className="w-3 h-3 fill-current" />
                      {product.rating}
                    </span>
                  )}
                </div>

                {/* Quick View Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileHover={{ opacity: 1, scale: 1 }}
                  className="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-luxury-black hover:bg-luxury-gold hover:text-luxury-black transition-all duration-300 opacity-0 group-hover:opacity-100"
                >
                  <Eye className="w-5 h-5" />
                </motion.button>

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-luxury-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>

              {/* Product Info */}
              <div className="p-6">
                <div className="mb-2">
                  <span className="text-sm text-luxury-gold font-medium uppercase tracking-wide">
                    {product.brand}
                  </span>
                </div>
                <h3 className="text-lg font-bold text-luxury-black mb-2 group-hover:text-luxury-gold transition-colors">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-600 mb-4 capitalize">
                  {product.category}
                </p>
                
                {/* Price */}
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-luxury-black">
                    ${product.price.toLocaleString()}
                  </span>
                  <Link
                    href={`/catalog/${product.id}`}
                    className="inline-flex items-center text-luxury-gold hover:text-luxury-gold-dark transition-colors font-medium"
                  >
                    View
                    <ArrowRight className="ml-1 w-4 h-4" />
                  </Link>
                </div>
              </div>

              {/* Card Border Animation */}
              <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-luxury-gold/30 transition-colors duration-300" />
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link
            href={viewAllLink}
            className="group inline-flex items-center px-8 py-4 bg-luxury-black text-luxury-cream font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-all duration-300 transform hover:scale-105"
          >
            View All {type === 'watches' ? 'Timepieces' : 'Jewelry'}
            <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

// Featured Products Section
export function FeaturedProducts({ products }: { products: any[] }) {
  return (
    <ProductShowcase
      title="Featured Timepieces"
      subtitle="Discover our handpicked selection of exceptional watches from the world's most prestigious brands"
      products={products}
      viewAllLink="/catalog?featured=true"
      type="watches"
    />
  )
}

// New Arrivals Section
export function NewArrivals({ products }: { products: any[] }) {
  return (
    <ProductShowcase
      title="New Arrivals"
      subtitle="Be the first to discover the latest additions to our luxury collection"
      products={products}
      viewAllLink="/catalog?new=true"
      type="watches"
    />
  )
}

// Jewelry Showcase Section
export function JewelryShowcase({ products }: { products: any[] }) {
  return (
    <ProductShowcase
      title="Exquisite Jewelry"
      subtitle="Timeless pieces that complement your luxury timepiece collection"
      products={products}
      viewAllLink="/catalog?category=jewelry"
      type="jewelry"
    />
  )
}
