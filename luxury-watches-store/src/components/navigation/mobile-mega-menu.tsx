'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronRight,
  ChevronDown,
  X,
  Clock,
  Crown,
  Gem,
  Star,
  ArrowRight,
  Settings
} from 'lucide-react'
import { LUXURY_BRANDS, WATCH_CATEGORIES, WATCH_COLLECTIONS } from '@/lib/constants'
import { getFeaturedProducts } from '@/lib/products'

interface MobileMegaMenuProps {
  isOpen: boolean
  onClose: () => void
}

export function MobileMegaMenu({ isOpen, onClose }: MobileMegaMenuProps) {
  const [activeSection, setActiveSection] = useState<string | null>(null)
  const featuredProducts = getFeaturedProducts(3)

  const menuVariants = {
    hidden: { 
      opacity: 0,
      x: '100%'
    },
    visible: { 
      opacity: 1,
      x: 0,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 200
      }
    },
    exit: { 
      opacity: 0,
      x: '100%',
      transition: {
        duration: 0.2
      }
    }
  }

  const sectionVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: { 
      opacity: 1, 
      height: 'auto',
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  }

  const toggleSection = (section: string) => {
    setActiveSection(activeSection === section ? null : section)
  }

  const mainMenuItems = [
    { name: 'Timepieces', section: 'watches', icon: Clock },
    { name: 'Brands', section: 'brands', icon: Crown },
    { name: 'Collections', href: '/collections', icon: Gem },
    { name: 'Heritage', section: 'heritage', icon: Star },
    { name: 'Services', section: 'services', icon: Settings }
  ]

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            onClick={onClose}
          />

          {/* Menu Panel */}
          <motion.div
            variants={menuVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 md:hidden overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <span className="text-luxury-black font-bold text-sm">LT</span>
                </div>
                <span className="font-luxury-serif text-xl font-bold text-luxury-black">
                  Menu
                </span>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Menu Content */}
            <div className="p-6">
              {/* Main Navigation */}
              <div className="space-y-2 mb-8">
                {mainMenuItems.map((item) => (
                  <div key={item.name}>
                    {item.href ? (
                      <Link
                        href={item.href}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-luxury-cream transition-colors"
                        onClick={onClose}
                      >
                        <div className="flex items-center gap-3">
                          {item.icon && <item.icon className="w-5 h-5 text-luxury-gold" />}
                          <span className="font-medium text-luxury-black">{item.name}</span>
                        </div>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      </Link>
                    ) : (
                      <>
                        <button
                          onClick={() => toggleSection(item.section!)}
                          className="flex items-center justify-between w-full p-3 rounded-lg hover:bg-luxury-cream transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            {item.icon && <item.icon className="w-5 h-5 text-luxury-gold" />}
                            <span className="font-medium text-luxury-black">{item.name}</span>
                          </div>
                          <motion.div
                            animate={{ rotate: activeSection === item.section ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDown className="w-4 h-4 text-gray-400" />
                          </motion.div>
                        </button>

                        {/* Expandable Section */}
                        <AnimatePresence>
                          {activeSection === item.section && (
                            <motion.div
                              variants={sectionVariants}
                              initial="hidden"
                              animate="visible"
                              exit="hidden"
                              className="overflow-hidden"
                            >
                              {item.section === 'watches' && (
                                <div className="ml-8 mt-2 space-y-4">
                                  {/* Categories */}
                                  <div>
                                    <h4 className="font-medium text-luxury-black mb-2">Categories</h4>
                                    <div className="space-y-2">
                                      {WATCH_CATEGORIES.slice(0, 4).map((category) => (
                                        <Link
                                          key={category.slug}
                                          href={`/catalog?category=${category.slug}`}
                                          className="block p-2 text-sm text-gray-600 hover:text-luxury-gold transition-colors"
                                          onClick={onClose}
                                        >
                                          {category.name}
                                        </Link>
                                      ))}
                                    </div>
                                  </div>

                                  {/* Collections */}
                                  <div>
                                    <h4 className="font-medium text-luxury-black mb-2">Collections</h4>
                                    <div className="space-y-2">
                                      {WATCH_COLLECTIONS.slice(0, 3).map((collection) => (
                                        <Link
                                          key={collection.slug}
                                          href={`/collections/${collection.slug}`}
                                          className="block p-2 text-sm text-gray-600 hover:text-luxury-gold transition-colors"
                                          onClick={onClose}
                                        >
                                          {collection.name}
                                        </Link>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              )}

                              {item.section === 'brands' && (
                                <div className="ml-8 mt-2">
                                  <div className="grid grid-cols-2 gap-2">
                                    {LUXURY_BRANDS.slice(0, 6).map((brand) => (
                                      <Link
                                        key={brand.slug}
                                        href={`/brands/${brand.slug}`}
                                        className="block p-2 text-sm text-gray-600 hover:text-luxury-gold transition-colors"
                                        onClick={onClose}
                                      >
                                        {brand.name}
                                      </Link>
                                    ))}
                                  </div>
                                  <Link
                                    href="/brands"
                                    className="inline-flex items-center gap-1 mt-3 text-sm text-luxury-gold hover:text-luxury-gold-dark"
                                    onClick={onClose}
                                  >
                                    View All
                                    <ArrowRight className="w-3 h-3" />
                                  </Link>
                                </div>
                              )}

                              {item.section === 'heritage' && (
                                <div className="ml-8 mt-2 space-y-3">
                                  {[
                                    { name: 'Our Story', href: '/about' },
                                    { name: 'Journal', href: '/blog' },
                                    { name: 'Craftsmanship', href: '/craftsmanship' },
                                    { name: 'Values', href: '/values' }
                                  ].map((link) => (
                                    <Link
                                      key={link.name}
                                      href={link.href}
                                      className="block p-2 text-sm text-gray-600 hover:text-luxury-gold transition-colors"
                                      onClick={onClose}
                                    >
                                      {link.name}
                                    </Link>
                                  ))}
                                </div>
                              )}

                              {item.section === 'services' && (
                                <div className="ml-8 mt-2 space-y-3">
                                  {[
                                    { name: 'Authentication', href: '/services/authentication' },
                                    { name: 'Maintenance', href: '/services/maintenance' },
                                    { name: 'Appraisal', href: '/services/appraisal' },
                                    { name: 'Contact Support', href: '/contact' }
                                  ].map((service) => (
                                    <Link
                                      key={service.name}
                                      href={service.href}
                                      className="block p-2 text-sm text-gray-600 hover:text-luxury-gold transition-colors"
                                      onClick={onClose}
                                    >
                                      {service.name}
                                    </Link>
                                  ))}
                                </div>
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </>
                    )}
                  </div>
                ))}
              </div>

              {/* Featured Products */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="font-luxury-serif text-lg font-semibold text-luxury-black mb-4 flex items-center gap-2">
                  <Star className="w-5 h-5 text-luxury-gold" />
                  Featured
                </h3>
                <div className="space-y-3">
                  {featuredProducts.map((product) => (
                    <Link
                      key={product.id}
                      href={`/catalog/${product.id}`}
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-luxury-cream transition-colors"
                      onClick={onClose}
                    >
                      <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                          src={product.images[0] || '/images/placeholder-watch.svg'}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-luxury-black text-sm">
                          {product.name}
                        </h4>
                        <p className="text-xs text-gray-500">{product.brand}</p>
                        <p className="text-xs font-semibold text-luxury-gold">
                          ${product.price.toLocaleString()}
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>

              {/* CTA Section */}
              <div className="border-t border-gray-200 pt-6 mt-6">
                <div className="bg-gradient-to-r from-luxury-gold to-luxury-gold-dark rounded-lg p-4 text-luxury-black">
                  <h4 className="font-luxury-serif font-bold mb-2">New Arrivals</h4>
                  <p className="text-sm mb-3 opacity-90">
                    Discover the latest luxury timepieces
                  </p>
                  <Link
                    href="/catalog?sort=newest"
                    className="inline-flex items-center gap-2 bg-luxury-black text-luxury-white px-4 py-2 rounded-lg text-sm font-medium"
                    onClick={onClose}
                  >
                    Explore Now
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
