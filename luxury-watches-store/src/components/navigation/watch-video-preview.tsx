'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Play, Pause } from 'lucide-react'

interface WatchVideoPreviewProps {
  videoSrc: string
  posterSrc: string
  title: string
  className?: string
}

export function WatchVideoPreview({ 
  videoSrc, 
  posterSrc, 
  title, 
  className = '' 
}: WatchVideoPreviewProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleEnded = () => {
      setIsPlaying(false)
      video.currentTime = 0
    }

    video.addEventListener('ended', handleEnded)
    return () => video.removeEventListener('ended', handleEnded)
  }, [])

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
      setIsPlaying(false)
    } else {
      video.play()
      setIsPlaying(true)
    }
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
    const video = videoRef.current
    if (video && !isPlaying) {
      video.play()
      setIsPlaying(true)
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    // Don't auto-pause on mouse leave to allow user to continue watching
  }

  return (
    <div 
      className={`relative group cursor-pointer rounded-lg overflow-hidden ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={togglePlay}
    >
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        poster={posterSrc}
        muted
        loop
        playsInline
        preload="metadata"
      >
        <source src={videoSrc} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* Play/Pause Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: isHovered || !isPlaying ? 1 : 0,
          scale: isHovered || !isPlaying ? 1 : 0.8
        }}
        transition={{ duration: 0.2 }}
        className="absolute inset-0 flex items-center justify-center"
      >
        <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
          {isPlaying ? (
            <Pause className="w-6 h-6 text-luxury-black" />
          ) : (
            <Play className="w-6 h-6 text-luxury-black ml-1" />
          )}
        </div>
      </motion.div>

      {/* Title Overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
        <h4 className="text-white font-medium text-sm">{title}</h4>
      </div>

      {/* Progress Bar */}
      {isPlaying && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
          <motion.div
            className="h-full bg-luxury-gold"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 4, ease: 'linear' }}
          />
        </div>
      )}
    </div>
  )
}
