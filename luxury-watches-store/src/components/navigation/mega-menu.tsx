'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChevronDown, 
  ArrowRight, 
  Star, 
  TrendingUp, 
  Award,
  Clock,
  Gem,
  Crown
} from 'lucide-react'
import { LUXURY_BRANDS, WATCH_CATEGORIES, WATCH_COLLECTIONS } from '@/lib/constants'
import { getFeaturedProducts } from '@/lib/products'
import { WatchVideoPreview } from './watch-video-preview'
import { BrandLogo } from '@/components/ui/brand-logo'
import { AnimatedBrandGrid, BrandSpotlight } from './animated-brand-grid'

interface MegaMenuProps {
  isOpen: boolean
  onClose: () => void
  activeMenu: string | null
  onMenuChange: (menu: string | null) => void
}

export function MegaMenu({ isOpen, onClose, activeMenu, onMenuChange }: MegaMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)
  const featuredProducts = getFeaturedProducts(3)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  const menuVariants = {
    hidden: { 
      opacity: 0, 
      y: -20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      scale: 0.95,
      transition: {
        duration: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.3
      }
    })
  }

  const renderWatchesMenu = () => (
    <div className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] bg-white shadow-luxury-hover border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-8 py-12">
        <div className="grid grid-cols-12 gap-8">
          {/* Featured Brands Section */}
          <div className="col-span-2">
            <h3 className="font-luxury-serif text-lg font-bold text-luxury-black mb-6 flex items-center gap-2">
              <Crown className="w-5 h-5 text-luxury-gold" />
              Featured Brands
            </h3>
            <div className="space-y-4">
              {['Rolex', 'Patek Philippe', 'Audemars Piguet', 'Omega', 'Cartier', 'Breitling'].map((brand, index) => (
                <motion.div
                  key={brand}
                  custom={index}
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Link
                    href={`/brands/${brand.toLowerCase().replace(' ', '-')}`}
                    className="group block text-gray-700 hover:text-luxury-gold transition-colors text-sm font-medium"
                    onClick={onClose}
                  >
                    {brand}
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Collections Section */}
          <div className="col-span-2">
            <h3 className="font-luxury-serif text-lg font-bold text-luxury-black mb-6 flex items-center gap-2">
              <Gem className="w-5 h-5 text-luxury-gold" />
              Collections
            </h3>
            <div className="space-y-4">
              {['Men\'s Watches', 'Women\'s Watches', 'Dive Watches', 'Chronographs', 'Dress Watches', 'Vintage Collection'].map((collection, index) => (
                <motion.div
                  key={collection}
                  custom={index}
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Link
                    href={`/catalog?collection=${collection.toLowerCase().replace(/['\s]/g, '-')}`}
                    className="group block text-gray-700 hover:text-luxury-gold transition-colors text-sm font-medium"
                    onClick={onClose}
                  >
                    {collection}
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Editor's Choice Section */}
          <div className="col-span-2">
            <h3 className="font-luxury-serif text-lg font-bold text-luxury-black mb-6 flex items-center gap-2">
              <Award className="w-5 h-5 text-luxury-gold" />
              Editor's Choice
            </h3>
            <div className="space-y-4">
              {['Under US$10,000', 'Under US$20,000', 'Under US$30,000', 'Under US$40,000', 'Accessories', 'Royal Oak'].map((choice, index) => (
                <motion.div
                  key={choice}
                  custom={index}
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Link
                    href={`/catalog?price=${choice.toLowerCase().replace(/[us$,\s]/g, '-')}`}
                    className="group block text-gray-700 hover:text-luxury-gold transition-colors text-sm font-medium"
                    onClick={onClose}
                  >
                    {choice}
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Top Categories Section */}
          <div className="col-span-2">
            <h3 className="font-luxury-serif text-lg font-bold text-luxury-black mb-6 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-luxury-gold" />
              Top Categories
            </h3>
            <div className="space-y-4">
              {['Aquanaut', 'Daytona', 'Accessories', 'Black Face Rolex', 'Factory Diamond', 'Green Dial'].map((category, index) => (
                <motion.div
                  key={category}
                  custom={index}
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Link
                    href={`/catalog?category=${category.toLowerCase().replace(/\s/g, '-')}`}
                    className="group block text-gray-700 hover:text-luxury-gold transition-colors text-sm font-medium"
                    onClick={onClose}
                  >
                    {category}
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Featured Product Showcase */}
          <div className="col-span-4">
            <div className="grid grid-cols-2 gap-4 h-full">
              {/* Large Featured Product */}
              <div className="col-span-2">
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2, duration: 0.4 }}
                  className="relative h-48 rounded-xl overflow-hidden group cursor-pointer"
                >
                  <Image
                    src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=400&fit=crop&crop=center"
                    alt="Luxury Watch Rental"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors">
                    <div className="absolute bottom-4 left-4 text-white">
                      <h4 className="font-luxury-serif text-lg font-bold mb-1">LUXURY WATCH RENTAL</h4>
                      <p className="text-sm opacity-90">Want to Rent a Watch for an Special Occasion?</p>
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Small Featured Product */}
              <div className="col-span-2">
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                  className="relative h-32 rounded-xl overflow-hidden group cursor-pointer"
                >
                  <Image
                    src="https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=600&h=300&fit=crop&crop=center"
                    alt="Live Inventory"
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors">
                    <div className="absolute bottom-4 left-4 text-white">
                      <h4 className="font-luxury-serif text-base font-bold mb-1">LIVE INVENTORY</h4>
                      <p className="text-xs opacity-90">Check out our live inventory</p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )



  const renderBrandsMenu = () => (
    <div className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] bg-white shadow-luxury-hover border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-8 py-12">
        <div className="grid grid-cols-12 gap-12">
          {/* Premium Brands Grid - 50% */}
          <div className="col-span-6">
            <div className="pr-8 border-r border-gray-100">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <Crown className="w-4 h-4 text-luxury-black" />
                </div>
                Prestigious Brands
              </h3>
              <div className="mb-8">
                <AnimatedBrandGrid
                  onClose={onClose}
                  maxBrands={12}
                  columns={3}
                />
              </div>
              <Link
                href="/brands"
                className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark font-semibold transition-colors group"
                onClick={onClose}
              >
                Discover All Brands
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>

          {/* Brand Spotlight - 50% */}
          <div className="col-span-6">
            <div className="pl-8">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <Award className="w-4 h-4 text-luxury-black" />
                </div>
                Heritage Spotlight
              </h3>
              <BrandSpotlight
                onClose={onClose}
                maxBrands={3}
              />

              {/* Brand Categories */}
              <div className="mt-8 pt-6 border-t border-gray-100">
                <h4 className="font-semibold text-luxury-black mb-4">Browse by Heritage</h4>
                <div className="flex flex-wrap gap-2">
                  {['Swiss Made', 'German Engineering', 'Japanese Precision', 'Italian Design'].map((category) => (
                    <Link
                      key={category}
                      href={`/brands?category=${category.toLowerCase().replace(' ', '-')}`}
                      className="px-3 py-2 bg-luxury-cream hover:bg-luxury-gold hover:text-luxury-black text-gray-700 rounded-lg text-sm font-medium transition-colors"
                      onClick={onClose}
                    >
                      {category}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderHeritageMenu = () => (
    <div className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] bg-white shadow-luxury-hover border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-8 py-12">
        <div className="grid grid-cols-12 gap-12">
          {/* Our Story - 40% */}
          <div className="col-span-5">
            <div className="pr-8 border-r border-gray-100">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <Award className="w-4 h-4 text-luxury-black" />
                </div>
                Our Heritage
              </h3>
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <Link
                    href="/about"
                    className="group block p-6 rounded-xl bg-gradient-to-br from-luxury-cream/50 to-white hover:from-luxury-cream hover:to-luxury-cream/80 transition-all duration-300 mega-menu-item"
                    onClick={onClose}
                  >
                    <h4 className="font-luxury-serif text-lg font-bold text-luxury-black group-hover:text-luxury-gold transition-colors mb-3">
                      Our Story
                    </h4>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      Discover the passion and craftsmanship behind our curated selection of the world's finest timepieces.
                    </p>
                    <span className="inline-flex items-center gap-2 text-luxury-gold font-semibold group-hover:gap-3 transition-all">
                      Learn More
                      <ArrowRight className="w-4 h-4" />
                    </span>
                  </Link>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Link
                    href="/blog"
                    className="group block p-6 rounded-xl bg-gradient-to-br from-luxury-cream/50 to-white hover:from-luxury-cream hover:to-luxury-cream/80 transition-all duration-300 mega-menu-item"
                    onClick={onClose}
                  >
                    <h4 className="font-luxury-serif text-lg font-bold text-luxury-black group-hover:text-luxury-gold transition-colors mb-3">
                      Journal
                    </h4>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      Explore insights, stories, and expertise from the world of luxury horology.
                    </p>
                    <span className="inline-flex items-center gap-2 text-luxury-gold font-semibold group-hover:gap-3 transition-all">
                      Read Articles
                      <ArrowRight className="w-4 h-4" />
                    </span>
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Craftsmanship - 35% */}
          <div className="col-span-4">
            <div className="px-6">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <Gem className="w-4 h-4 text-luxury-black" />
                </div>
                Craftsmanship
              </h3>
              <div className="space-y-4">
                {[
                  { title: 'Swiss Excellence', desc: 'Precision and tradition from the heart of Switzerland' },
                  { title: 'Master Artisans', desc: 'Skilled craftsmen preserving centuries-old techniques' },
                  { title: 'Innovation', desc: 'Cutting-edge technology meets timeless design' },
                  { title: 'Quality Assurance', desc: 'Rigorous testing and certification processes' }
                ].map((item, index) => (
                  <motion.div
                    key={item.title}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 + index * 0.1 }}
                    className="flex items-start gap-4 p-4 rounded-lg hover:bg-luxury-cream transition-colors"
                  >
                    <div className="w-2 h-2 bg-luxury-gold rounded-full mt-2" />
                    <div>
                      <h4 className="font-semibold text-luxury-black mb-1">{item.title}</h4>
                      <p className="text-sm text-gray-600">{item.desc}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Values - 25% */}
          <div className="col-span-3">
            <div className="pl-8">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <Crown className="w-4 h-4 text-luxury-black" />
                </div>
                Values
              </h3>
              <div className="space-y-6">
                {[
                  { icon: '🎯', title: 'Authenticity', desc: 'Every timepiece is guaranteed authentic' },
                  { icon: '⭐', title: 'Excellence', desc: 'Uncompromising quality in every detail' },
                  { icon: '🤝', title: 'Trust', desc: 'Building lasting relationships with collectors' }
                ].map((value, index) => (
                  <motion.div
                    key={value.title}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.2 + index * 0.1 }}
                    className="text-center p-4 rounded-xl bg-luxury-cream/30 hover:bg-luxury-cream/50 transition-colors"
                  >
                    <div className="text-2xl mb-2">{value.icon}</div>
                    <h4 className="font-semibold text-luxury-black mb-2">{value.title}</h4>
                    <p className="text-xs text-gray-600">{value.desc}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderServicesMenu = () => (
    <div className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] bg-white shadow-luxury-hover border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-8 py-12">
        <div className="grid grid-cols-12 gap-12">
          {/* Main Services - 50% */}
          <div className="col-span-6">
            <div className="pr-8 border-r border-gray-100">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <Star className="w-4 h-4 text-luxury-black" />
                </div>
                Premium Services
              </h3>
              <div className="grid grid-cols-2 gap-6">
                {[
                  {
                    title: 'Authentication',
                    desc: 'Expert verification of luxury timepieces',
                    icon: '🔍',
                    href: '/services/authentication'
                  },
                  {
                    title: 'Maintenance',
                    desc: 'Professional servicing and restoration',
                    icon: '🔧',
                    href: '/services/maintenance'
                  },
                  {
                    title: 'Appraisal',
                    desc: 'Certified valuation for insurance',
                    icon: '📋',
                    href: '/services/appraisal'
                  },
                  {
                    title: 'Consultation',
                    desc: 'Personal guidance from experts',
                    icon: '💬',
                    href: '/services/consultation'
                  }
                ].map((service, index) => (
                  <motion.div
                    key={service.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 + index * 0.1 }}
                  >
                    <Link
                      href={service.href}
                      className="group block p-6 rounded-xl bg-gradient-to-br from-luxury-cream/50 to-white hover:from-luxury-cream hover:to-luxury-cream/80 transition-all duration-300 mega-menu-item border border-transparent hover:border-luxury-gold/20"
                      onClick={onClose}
                    >
                      <div className="text-3xl mb-4">{service.icon}</div>
                      <h4 className="font-luxury-serif text-lg font-bold text-luxury-black group-hover:text-luxury-gold transition-colors mb-2">
                        {service.title}
                      </h4>
                      <p className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors">
                        {service.desc}
                      </p>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Support & Contact - 50% */}
          <div className="col-span-6">
            <div className="pl-8">
              <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-8 flex items-center gap-3">
                <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-luxury-black" />
                </div>
                Support & Contact
              </h3>

              {/* Contact Information */}
              <div className="space-y-6 mb-8">
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="p-6 rounded-xl bg-gradient-to-br from-luxury-gold/10 to-luxury-gold/5"
                >
                  <h4 className="font-semibold text-luxury-black mb-4">Get in Touch</h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-luxury-gold rounded-full"></span>
                      <span className="text-gray-600">Phone: +****************</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-luxury-gold rounded-full"></span>
                      <span className="text-gray-600">Email: <EMAIL></span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-luxury-gold rounded-full"></span>
                      <span className="text-gray-600">Hours: Mon-Sat 9AM-7PM EST</span>
                    </div>
                  </div>
                </motion.div>

                {/* Quick Actions */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className="space-y-3"
                >
                  <h4 className="font-semibold text-luxury-black mb-4">Quick Actions</h4>
                  {[
                    { title: 'Schedule Appointment', href: '/contact/appointment' },
                    { title: 'Track Your Order', href: '/account/orders' },
                    { title: 'Return & Exchange', href: '/support/returns' },
                    { title: 'Warranty Information', href: '/support/warranty' }
                  ].map((action, index) => (
                    <Link
                      key={action.title}
                      href={action.href}
                      className="group flex items-center justify-between p-3 rounded-lg hover:bg-luxury-cream transition-colors"
                      onClick={onClose}
                    >
                      <span className="text-luxury-black group-hover:text-luxury-gold transition-colors font-medium">
                        {action.title}
                      </span>
                      <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-luxury-gold group-hover:translate-x-1 transition-all" />
                    </Link>
                  ))}
                </motion.div>
              </div>

              {/* CTA */}
              <div className="pt-6 border-t border-gray-100">
                <Link
                  href="/contact"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
                  onClick={onClose}
                >
                  Contact Our Experts
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        ref={menuRef}
        variants={menuVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="absolute top-full left-0 right-0 mega-menu-panel shadow-luxury-hover border-t border-gray-100 z-50"
      >
        {activeMenu === 'watches' && renderWatchesMenu()}
        {activeMenu === 'brands' && renderBrandsMenu()}
        {activeMenu === 'heritage' && renderHeritageMenu()}
        {activeMenu === 'services' && renderServicesMenu()}
      </motion.div>
    </AnimatePresence>
  )
}
