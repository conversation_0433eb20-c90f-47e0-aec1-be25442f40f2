'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { BrandLogo } from '@/components/ui/brand-logo'
import { LUXURY_BRANDS } from '@/lib/constants'

interface AnimatedBrandGridProps {
  onClose: () => void
  maxBrands?: number
  columns?: number
}

export function AnimatedBrandGrid({ 
  onClose, 
  maxBrands = 12, 
  columns = 3 
}: AnimatedBrandGridProps) {
  const brands = LUXURY_BRANDS.slice(0, maxBrands)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.8
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  }

  const gridClasses = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    6: 'grid-cols-6'
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={`grid ${gridClasses[columns as keyof typeof gridClasses] || 'grid-cols-3'} gap-4`}
    >
      {brands.map((brand, index) => (
        <motion.div
          key={brand.slug}
          variants={itemVariants}
          whileHover={{ 
            scale: 1.05,
            transition: { duration: 0.2 }
          }}
          whileTap={{ scale: 0.95 }}
        >
          <Link
            href={`/brands/${brand.slug}`}
            className="group block p-4 rounded-xl hover:bg-luxury-cream transition-all duration-300 mega-menu-item text-center relative overflow-hidden"
            onClick={onClose}
          >
            {/* Background Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-luxury-gold/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
            
            {/* Brand Logo */}
            <div className="relative flex justify-center mb-3">
              <BrandLogo 
                brandName={brand.name}
                size="md"
                variant="circle"
                className="group-hover:scale-110 transition-transform duration-300 shadow-sm group-hover:shadow-md"
              />
            </div>
            
            {/* Brand Name */}
            <span className="relative font-semibold text-luxury-black group-hover:text-luxury-gold transition-colors duration-300 block mb-1 text-sm">
              {brand.name}
            </span>
            
            {/* Founded Year */}
            <p className="relative text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
              Est. {brand.founded}
            </p>
            
            {/* Hover Indicator */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-luxury-gold group-hover:w-8 transition-all duration-300" />
          </Link>
        </motion.div>
      ))}
    </motion.div>
  )
}

// Enhanced brand showcase for spotlight section
export function BrandSpotlight({ 
  onClose, 
  maxBrands = 3 
}: { 
  onClose: () => void
  maxBrands?: number 
}) {
  const brands = LUXURY_BRANDS.slice(0, maxBrands)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      x: 30,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      x: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {brands.map((brand, index) => (
        <motion.div
          key={brand.slug}
          variants={itemVariants}
          whileHover={{ 
            scale: 1.02,
            transition: { duration: 0.2 }
          }}
        >
          <Link
            href={`/brands/${brand.slug}`}
            className="group block bg-gradient-to-br from-luxury-cream/50 to-white rounded-xl p-6 hover:from-luxury-cream hover:to-luxury-cream/80 transition-all duration-300 mega-menu-item border border-transparent hover:border-luxury-gold/20 relative overflow-hidden"
            onClick={onClose}
          >
            {/* Background Pattern */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-luxury-gold/5 to-transparent rounded-full transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-transform duration-500" />
            
            <div className="relative flex items-start gap-6">
              {/* Brand Logo */}
              <div className="flex-shrink-0">
                <BrandLogo 
                  brandName={brand.name}
                  size="lg"
                  variant="square"
                  className="group-hover:scale-105 transition-transform duration-300 shadow-lg group-hover:shadow-xl"
                />
              </div>
              
              {/* Brand Info */}
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h4 className="font-luxury-serif text-lg font-bold text-luxury-black group-hover:text-luxury-gold transition-colors duration-300">
                    {brand.name}
                  </h4>
                  <motion.span 
                    className="px-2 py-1 bg-luxury-gold/20 text-luxury-gold text-xs font-semibold rounded-full"
                    whileHover={{ scale: 1.1 }}
                  >
                    EST. {brand.founded}
                  </motion.span>
                </div>
                <p className="text-sm text-gray-600 line-clamp-2 group-hover:text-gray-700 transition-colors duration-300">
                  {brand.description}
                </p>
              </div>
              
              {/* Arrow Indicator */}
              <motion.div
                className="flex-shrink-0 text-gray-400 group-hover:text-luxury-gold transition-colors duration-300"
                whileHover={{ x: 5 }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.div>
            </div>
          </Link>
        </motion.div>
      ))}
    </motion.div>
  )
}
