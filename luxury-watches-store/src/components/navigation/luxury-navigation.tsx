'use client'

import { useState, useRef } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { ChevronDown } from 'lucide-react'
import { MegaMenu } from './mega-menu'

interface NavigationItem {
  name: string
  href?: string
  hasMegaMenu?: boolean
  megaMenuType?: string
}

const navigationItems: NavigationItem[] = [
  { name: 'Timepieces', hasMegaMenu: true, megaMenuType: 'watches' },
  { name: 'Brands', hasMegaMenu: true, megaMenuType: 'brands' },
  { name: 'Collections', href: '/collections' },
  { name: 'Blog', href: '/blog' },
  { name: 'Heritage', hasMegaMenu: true, megaMenuType: 'heritage' },
  { name: 'Services', hasMegaMenu: true, megaMenuType: 'services' }
]

export function LuxuryNavigation() {
  const [activeMenu, setActiveMenu] = useState<string | null>(null)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout>()

  const handleMouseEnter = (menuType: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setActiveMenu(menuType)
    setIsMenuOpen(true)
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveMenu(null)
      setIsMenuOpen(false)
    }, 150)
  }

  const handleMenuMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }

  const handleMenuMouseLeave = () => {
    setActiveMenu(null)
    setIsMenuOpen(false)
  }

  const closeMenu = () => {
    setActiveMenu(null)
    setIsMenuOpen(false)
  }

  return (
    <div className="relative">
      <nav className="flex items-center space-x-8">
        {navigationItems.map((item) => (
          <div
            key={item.name}
            className="relative"
            onMouseEnter={item.hasMegaMenu ? () => handleMouseEnter(item.megaMenuType!) : undefined}
            onMouseLeave={item.hasMegaMenu ? handleMouseLeave : undefined}
          >
            {item.href ? (
              <Link
                href={item.href}
                className="flex items-center space-x-1 text-luxury-black hover:text-luxury-gold transition-colors font-medium py-2 px-1"
              >
                <span>{item.name}</span>
              </Link>
            ) : (
              <button className="flex items-center space-x-1 text-luxury-black hover:text-luxury-gold transition-colors font-medium py-2 px-1">
                <span>{item.name}</span>
                {item.hasMegaMenu && (
                  <motion.div
                    animate={{ rotate: activeMenu === item.megaMenuType ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </motion.div>
                )}
              </button>
            )}

            {/* Active indicator */}
            {activeMenu === item.megaMenuType && (
              <motion.div
                layoutId="activeIndicator"
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-luxury-gold"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.2 }}
              />
            )}
          </div>
        ))}
      </nav>

      {/* Mega Menu */}
      <div
        onMouseEnter={handleMenuMouseEnter}
        onMouseLeave={handleMenuMouseLeave}
      >
        <MegaMenu
          isOpen={isMenuOpen}
          onClose={closeMenu}
          activeMenu={activeMenu}
          onMenuChange={setActiveMenu}
        />
      </div>
    </div>
  )
}
