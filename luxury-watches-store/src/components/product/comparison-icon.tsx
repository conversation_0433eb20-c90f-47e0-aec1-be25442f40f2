'use client';

import { BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useComparison } from '@/contexts/comparison-context';
import { cn } from '@/lib/utils';

interface ComparisonIconProps {
  className?: string;
}

export function ComparisonIcon({ className }: ComparisonIconProps) {
  const { itemCount, openComparison } = useComparison();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={openComparison}
      className={cn("text-luxury-black hover:text-luxury-gold relative", className)}
    >
      <BarChart3 className="h-5 w-5" />
      {itemCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-luxury-gold text-luxury-black text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
          {itemCount > 99 ? '99+' : itemCount}
        </span>
      )}
    </Button>
  );
}
