'use client'

import { useState } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Eye, Download, Edit, Trash2, Copy } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImagePreviewProps {
  src: string
  alt: string
  className?: string
  aspectRatio?: 'square' | 'video' | 'auto'
  showOverlay?: boolean
  onEdit?: () => void
  onDelete?: () => void
  onCopy?: () => void
  onDownload?: () => void
}

export function ImagePreview({
  src,
  alt,
  className,
  aspectRatio = 'square',
  showOverlay = true,
  onEdit,
  onDelete,
  onCopy,
  onDownload
}: ImagePreviewProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [showFullscreen, setShowFullscreen] = useState(false)

  const aspectClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    auto: 'aspect-auto'
  }

  const handleImageLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleImageError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  return (
    <>
      <div className={cn(
        'relative group rounded-lg overflow-hidden bg-gray-100',
        aspectClasses[aspectRatio],
        className
      )}>
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-luxury-gold border-t-transparent rounded-full animate-spin" />
          </div>
        )}

        {/* Error State */}
        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <div className="w-8 h-8 mx-auto mb-2 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <p className="text-xs text-gray-500">Failed to load</p>
            </div>
          </div>
        )}

        {/* Image */}
        {!hasError && (
          <Image
            src={src}
            alt={alt}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        )}

        {/* Overlay */}
        {showOverlay && !hasError && (
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="flex gap-2">
              <button
                onClick={() => setShowFullscreen(true)}
                className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                title="View fullscreen"
              >
                <Eye className="w-4 h-4 text-white" />
              </button>
              
              {onCopy && (
                <button
                  onClick={onCopy}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  title="Copy URL"
                >
                  <Copy className="w-4 h-4 text-white" />
                </button>
              )}
              
              {onDownload && (
                <button
                  onClick={onDownload}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  title="Download"
                >
                  <Download className="w-4 h-4 text-white" />
                </button>
              )}
              
              {onEdit && (
                <button
                  onClick={onEdit}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  title="Edit"
                >
                  <Edit className="w-4 h-4 text-white" />
                </button>
              )}
              
              {onDelete && (
                <button
                  onClick={onDelete}
                  className="p-2 bg-red-500/80 rounded-lg hover:bg-red-500 transition-colors"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4 text-white" />
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      <AnimatePresence>
        {showFullscreen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setShowFullscreen(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-4xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setShowFullscreen(false)}
                className="absolute top-4 right-4 p-2 bg-black/50 rounded-lg hover:bg-black/70 transition-colors z-10"
              >
                <X className="w-5 h-5 text-white" />
              </button>
              
              <div className="relative">
                <Image
                  src={src}
                  alt={alt}
                  width={800}
                  height={600}
                  className="max-w-full max-h-[80vh] object-contain rounded-lg"
                />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

// Grid component for multiple images
interface ImageGridProps {
  images: Array<{
    src: string
    alt: string
    id?: string
  }>
  columns?: number
  aspectRatio?: 'square' | 'video' | 'auto'
  onImageEdit?: (index: number) => void
  onImageDelete?: (index: number) => void
  className?: string
}

export function ImageGrid({
  images,
  columns = 4,
  aspectRatio = 'square',
  onImageEdit,
  onImageDelete,
  className
}: ImageGridProps) {
  const gridClasses = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6'
  }

  return (
    <div className={cn(
      'grid gap-4',
      gridClasses[columns as keyof typeof gridClasses] || 'grid-cols-4',
      className
    )}>
      {images.map((image, index) => (
        <ImagePreview
          key={image.id || index}
          src={image.src}
          alt={image.alt}
          aspectRatio={aspectRatio}
          onEdit={onImageEdit ? () => onImageEdit(index) : undefined}
          onDelete={onImageDelete ? () => onImageDelete(index) : undefined}
        />
      ))}
    </div>
  )
}
