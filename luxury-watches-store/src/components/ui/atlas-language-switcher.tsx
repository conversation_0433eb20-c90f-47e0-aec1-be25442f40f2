'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline'
import { useLanguageSwitcher } from '@/lib/i18n/hooks'
import { cn } from '@/lib/utils'

interface AtlasLanguageSwitcherProps {
  variant?: 'default' | 'compact' | 'mobile'
  className?: string
  showFlag?: boolean
  showName?: boolean
}

export function AtlasLanguageSwitcher({ 
  variant = 'default',
  className,
  showFlag = true,
  showName = true
}: AtlasLanguageSwitcherProps) {
  const {
    currentLanguage,
    supportedLanguages,
    isOpen,
    toggleSwitcher,
    selectLanguage,
    closeSwitcher
  } = useLanguageSwitcher()

  const dropdownRef = useRef<HTMLDivElement>(null)
  const currentLang = supportedLanguages.find(lang => lang.code === currentLanguage)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        closeSwitcher()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, closeSwitcher])

  const variants = {
    default: {
      button: 'px-4 py-2 text-sm',
      dropdown: 'w-48',
      item: 'px-4 py-3'
    },
    compact: {
      button: 'px-3 py-1.5 text-xs',
      dropdown: 'w-40',
      item: 'px-3 py-2'
    },
    mobile: {
      button: 'px-4 py-3 text-base',
      dropdown: 'w-full',
      item: 'px-4 py-4'
    }
  }

  const variantClasses = variants[variant]

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      <button
        onClick={toggleSwitcher}
        className={cn(
          'flex items-center gap-2 text-gray-700 hover:text-luxury-gold transition-colors',
          variantClasses.button
        )}
        aria-label="Select language"
      >
        <GlobeAltIcon className="w-4 h-4" />
        
        {showFlag && currentLang && (
          <span className="text-lg" role="img" aria-label={currentLang.name}>
            {currentLang.flag}
          </span>
        )}
        
        {showName && currentLang && (
          <span className="hidden sm:inline">
            {variant === 'compact' ? currentLang.code.toUpperCase() : currentLang.nativeName}
          </span>
        )}
        
        <ChevronDownIcon 
          className={cn(
            'w-4 h-4 transition-transform',
            isOpen && 'rotate-180'
          )} 
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={cn(
              'absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50',
              variantClasses.dropdown
            )}
          >
            <div className="py-1">
              {supportedLanguages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => selectLanguage(language.code)}
                  className={cn(
                    'w-full flex items-center gap-3 text-left hover:bg-gray-50 transition-colors',
                    variantClasses.item,
                    currentLanguage === language.code ? 'bg-luxury-gold/10 text-luxury-gold' : 'text-gray-700'
                  )}
                >
                  <span className="text-lg" role="img" aria-label={language.name}>
                    {language.flag}
                  </span>
                  <div className="flex-1">
                    <div className="font-medium">{language.nativeName}</div>
                    {variant !== 'compact' && (
                      <div className="text-xs text-gray-500">{language.name}</div>
                    )}
                  </div>
                  {currentLanguage === language.code && (
                    <div className="w-2 h-2 bg-luxury-gold rounded-full" />
                  )}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Compact version for header/navigation
export function CompactLanguageSwitcher({ className }: { className?: string }) {
  return (
    <AtlasLanguageSwitcher
      variant="compact"
      className={className}
      showName={false}
    />
  )
}

// Mobile version for mobile menu
export function MobileLanguageSwitcher({ className }: { className?: string }) {
  return (
    <AtlasLanguageSwitcher
      variant="mobile"
      className={className}
    />
  )
}
