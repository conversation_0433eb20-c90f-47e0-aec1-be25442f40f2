'use client'

import { cn } from '@/lib/utils'

interface BrandLogoProps {
  brandName: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'circle' | 'square' | 'minimal'
  className?: string
}

export function BrandLogo({ 
  brandName, 
  size = 'md', 
  variant = 'circle',
  className 
}: BrandLogoProps) {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-12 h-12 text-sm',
    lg: 'w-16 h-16 text-base',
    xl: 'w-20 h-20 text-lg'
  }

  const variantClasses = {
    circle: 'rounded-full',
    square: 'rounded-lg',
    minimal: 'rounded-md'
  }

  // Generate brand-specific styling based on brand name
  const getBrandStyling = (brand: string) => {
    const brandLower = brand.toLowerCase()
    
    switch (brandLower) {
      case 'rolex':
        return {
          bg: 'bg-gradient-to-br from-green-700 to-green-900',
          text: 'text-yellow-300',
          logo: '👑'
        }
      case 'patek philippe':
        return {
          bg: 'bg-gradient-to-br from-blue-900 to-blue-950',
          text: 'text-gold-300',
          logo: '⚜️'
        }
      case 'audemars piguet':
        return {
          bg: 'bg-gradient-to-br from-gray-800 to-black',
          text: 'text-orange-400',
          logo: '🔷'
        }
      case 'omega':
        return {
          bg: 'bg-gradient-to-br from-red-700 to-red-900',
          text: 'text-white',
          logo: 'Ω'
        }
      case 'cartier':
        return {
          bg: 'bg-gradient-to-br from-red-600 to-red-800',
          text: 'text-yellow-300',
          logo: '💎'
        }
      case 'breitling':
        return {
          bg: 'bg-gradient-to-br from-yellow-600 to-yellow-800',
          text: 'text-black',
          logo: '✈️'
        }
      case 'tag heuer':
        return {
          bg: 'bg-gradient-to-br from-red-500 to-red-700',
          text: 'text-white',
          logo: '🏁'
        }
      case 'iwc':
        return {
          bg: 'bg-gradient-to-br from-blue-600 to-blue-800',
          text: 'text-white',
          logo: '⚙️'
        }
      case 'jaeger-lecoultre':
        return {
          bg: 'bg-gradient-to-br from-purple-700 to-purple-900',
          text: 'text-yellow-300',
          logo: '🌙'
        }
      case 'vacheron constantin':
        return {
          bg: 'bg-gradient-to-br from-indigo-800 to-indigo-950',
          text: 'text-gold-300',
          logo: '✨'
        }
      case 'panerai':
        return {
          bg: 'bg-gradient-to-br from-amber-700 to-amber-900',
          text: 'text-black',
          logo: '⚓'
        }
      case 'tudor':
        return {
          bg: 'bg-gradient-to-br from-red-800 to-black',
          text: 'text-yellow-300',
          logo: '🛡️'
        }
      case 'zenith':
        return {
          bg: 'bg-gradient-to-br from-blue-500 to-blue-700',
          text: 'text-white',
          logo: '⭐'
        }
      case 'hublot':
        return {
          bg: 'bg-gradient-to-br from-orange-600 to-orange-800',
          text: 'text-black',
          logo: '🔥'
        }
      case 'richard mille':
        return {
          bg: 'bg-gradient-to-br from-gray-700 to-gray-900',
          text: 'text-red-400',
          logo: '⚡'
        }
      case 'a. lange & söhne':
        return {
          bg: 'bg-gradient-to-br from-amber-600 to-amber-800',
          text: 'text-black',
          logo: '🏛️'
        }
      default:
        return {
          bg: 'bg-gradient-to-br from-luxury-gold to-luxury-gold-dark',
          text: 'text-luxury-black',
          logo: brand.charAt(0).toUpperCase()
        }
    }
  }

  const brandStyling = getBrandStyling(brandName)

  return (
    <div 
      className={cn(
        'flex items-center justify-center font-bold transition-all duration-300 hover:scale-110 hover:shadow-lg',
        sizeClasses[size],
        variantClasses[variant],
        brandStyling.bg,
        brandStyling.text,
        className
      )}
      title={brandName}
    >
      {brandStyling.logo}
    </div>
  )
}

// Preset brand logos for common luxury watch brands
export const BRAND_LOGOS = {
  'rolex': { emoji: '👑', colors: 'from-green-700 to-green-900' },
  'patek-philippe': { emoji: '⚜️', colors: 'from-blue-900 to-blue-950' },
  'audemars-piguet': { emoji: '🔷', colors: 'from-gray-800 to-black' },
  'omega': { emoji: 'Ω', colors: 'from-red-700 to-red-900' },
  'cartier': { emoji: '💎', colors: 'from-red-600 to-red-800' },
  'breitling': { emoji: '✈️', colors: 'from-yellow-600 to-yellow-800' },
  'tag-heuer': { emoji: '🏁', colors: 'from-red-500 to-red-700' },
  'iwc': { emoji: '⚙️', colors: 'from-blue-600 to-blue-800' },
  'jaeger-lecoultre': { emoji: '🌙', colors: 'from-purple-700 to-purple-900' },
  'vacheron-constantin': { emoji: '✨', colors: 'from-indigo-800 to-indigo-950' },
  'panerai': { emoji: '⚓', colors: 'from-amber-700 to-amber-900' },
  'tudor': { emoji: '🛡️', colors: 'from-red-800 to-black' },
  'zenith': { emoji: '⭐', colors: 'from-blue-500 to-blue-700' },
  'hublot': { emoji: '🔥', colors: 'from-orange-600 to-orange-800' },
  'richard-mille': { emoji: '⚡', colors: 'from-gray-700 to-gray-900' },
  'a-lange-sohne': { emoji: '🏛️', colors: 'from-amber-600 to-amber-800' }
} as const

export type BrandSlug = keyof typeof BRAND_LOGOS
