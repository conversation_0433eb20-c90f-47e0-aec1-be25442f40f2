'use client';

import { useState, useTransition } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { locales, localeConfig, type Locale } from '@/i18n/config';
import { cn } from '@/lib/utils';

export function LanguageSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale() as Locale;
  const t = useTranslations('language');

  const currentLocaleConfig = localeConfig[locale];

  const handleLocaleChange = (newLocale: Locale) => {
    if (newLocale === locale) {
      setIsOpen(false);
      return;
    }

    startTransition(() => {
      // Store the preference in localStorage
      localStorage.setItem('preferred-locale', newLocale);
      
      // Replace the current locale in the pathname
      const segments = pathname.split('/');
      segments[1] = newLocale;
      const newPathname = segments.join('/');
      
      router.replace(newPathname);
      setIsOpen(false);
    });
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        disabled={isPending}
        className={cn(
          "flex items-center gap-2 text-luxury-black hover:text-luxury-gold transition-colors",
          isPending && "opacity-50"
        )}
      >
        <Globe className="h-4 w-4" />
        <span className="hidden sm:inline">{currentLocaleConfig.flag}</span>
        <span className="hidden md:inline">{currentLocaleConfig.name}</span>
        <ChevronDown className={cn(
          "h-3 w-3 transition-transform",
          isOpen && "rotate-180"
        )} />
      </Button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-luxury border border-gray-200 py-2 z-50"
            >
              <div className="px-3 py-2 border-b border-gray-100">
                <p className="text-sm font-medium text-luxury-black">
                  {t('switchLanguage')}
                </p>
              </div>

              <div className="py-1">
                {locales.map((localeOption) => {
                  const config = localeConfig[localeOption];
                  const isActive = localeOption === locale;

                  return (
                    <button
                      key={localeOption}
                      onClick={() => handleLocaleChange(localeOption)}
                      disabled={isPending}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors",
                        isActive
                          ? "bg-luxury-cream text-luxury-gold font-medium"
                          : "text-gray-700 hover:bg-gray-50 hover:text-luxury-gold",
                        isPending && "opacity-50 cursor-not-allowed"
                      )}
                    >
                      <span className="text-lg">{config.flag}</span>
                      <div className="flex-1 text-left">
                        <div className="font-medium">{config.name}</div>
                        <div className="text-xs text-gray-500">
                          {config.market} Market
                        </div>
                      </div>
                      {isActive && (
                        <div className="w-2 h-2 bg-luxury-gold rounded-full" />
                      )}
                    </button>
                  );
                })}
              </div>

              <div className="px-3 py-2 border-t border-gray-100">
                <p className="text-xs text-gray-500">
                  Language preference is saved automatically
                </p>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for client-side locale detection and preference
export function useLocalePreference() {
  const locale = useLocale() as Locale;
  
  // Get preferred locale from localStorage or browser
  const getPreferredLocale = (): Locale => {
    if (typeof window === 'undefined') return locale;
    
    // Check localStorage first
    const stored = localStorage.getItem('preferred-locale') as Locale;
    if (stored && locales.includes(stored)) {
      return stored;
    }
    
    // Fall back to browser language detection
    const browserLang = navigator.language.split('-')[0] as Locale;
    if (locales.includes(browserLang)) {
      return browserLang;
    }
    
    return locale;
  };

  return {
    currentLocale: locale,
    preferredLocale: getPreferredLocale(),
    setPreferredLocale: (newLocale: Locale) => {
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferred-locale', newLocale);
      }
    }
  };
}
