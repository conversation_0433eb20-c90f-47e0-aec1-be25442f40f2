'use client';

import { ShoppingBag } from 'lucide-react';
import { motion } from 'framer-motion';
import { useCart } from '@/contexts/cart-context';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CartIconProps {
  className?: string;
}

export function CartIcon({ className }: CartIconProps) {
  const { itemCount, toggleCart } = useCart();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleCart}
      className={cn(
        "relative text-luxury-black hover:text-luxury-gold transition-colors",
        className
      )}
    >
      <ShoppingBag className="h-6 w-6" />
      
      {itemCount > 0 && (
        <motion.span
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-1 -right-1 bg-luxury-gold text-luxury-black text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]"
        >
          {itemCount > 99 ? '99+' : itemCount}
        </motion.span>
      )}
    </Button>
  );
}
