'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight, Clock, Gem, Star, Eye } from 'lucide-react'

interface ProductShowcaseProps {
  title: string
  subtitle: string
  products: Array<{
    id: string
    name: string
    brand: string
    price: number
    image: string
    category: string
    rating?: number
    isNew?: boolean
  }>
  viewAllLink: string
  type: 'watches' | 'jewelry'
}

export function ProductShowcase({ title, subtitle, products, viewAllLink, type }: ProductShowcaseProps) {
  const icon = type === 'watches' ? Clock : Gem
  const IconComponent = icon

  return (
    <section className="py-20 bg-luxury-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-luxury-gold/10 rounded-full px-4 py-2 mb-4">
            <IconComponent className="w-4 h-4 text-luxury-gold" />
            <span className="text-luxury-gold text-sm font-medium uppercase tracking-wide">
              {type === 'watches' ? 'Timepieces' : 'Fine Jewelry'}
            </span>
          </div>
          
          <h2 className="font-luxury-serif text-4xl md:text-5xl font-bold text-luxury-black mb-4">
            {title}
          </h2>
          
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-luxury-gold/30">
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden">
                  {/* Background Image */}
                  <Image
                    src={product.image}
                    alt={`${product.brand} ${product.name}`}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                    onError={(e) => {
                      // Fallback to gradient background if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />

                  {/* Fallback gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                    <div className="w-32 h-32 bg-luxury-charcoal rounded-full flex items-center justify-center">
                      <IconComponent className="w-16 h-16 text-luxury-gold" />
                    </div>
                  </div>

                  {product.isNew && (
                    <div className="absolute top-4 left-4 z-10 bg-luxury-gold text-luxury-black text-xs font-bold px-3 py-1 rounded-full">
                      NEW
                    </div>
                  )}
                  
                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-luxury-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <Link
                      href={`/catalog/${product.id}`}
                      className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black font-semibold px-6 py-3 rounded-lg transform translate-y-4 group-hover:translate-y-0 transition-all duration-300 inline-flex items-center gap-2"
                    >
                      <Eye className="w-4 h-4" />
                      View Details
                    </Link>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-luxury-gold font-medium uppercase tracking-wide">
                      {product.brand}
                    </span>
                    {product.rating && (
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-luxury-gold text-luxury-gold" />
                        <span className="text-sm text-gray-600">{product.rating}</span>
                      </div>
                    )}
                  </div>
                  
                  <h3 className="font-semibold text-luxury-black mb-2 group-hover:text-luxury-gold transition-colors">
                    {product.name}
                  </h3>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-luxury-black">
                      ${product.price.toLocaleString()}
                    </span>
                    <span className="text-sm text-gray-500 capitalize">
                      {product.category}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link
            href={viewAllLink}
            className="group inline-flex items-center gap-2 bg-luxury-black hover:bg-luxury-charcoal text-luxury-white font-semibold px-8 py-4 rounded-lg transition-all duration-300 hover:shadow-xl"
          >
            View All {type === 'watches' ? 'Timepieces' : 'Jewelry'}
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

// Category Showcase Component
export function CategoryShowcase() {
  const categories = [
    {
      name: 'Luxury Watches',
      description: 'Swiss precision meets timeless elegance',
      icon: Clock,
      href: '/catalog',
      image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600&fit=crop&crop=center'
    },
    {
      name: 'Fine Jewelry',
      description: 'Exquisite pieces crafted with precious gems',
      icon: Gem,
      href: '/catalog',
      image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800&h=600&fit=crop&crop=center'
    }
  ]

  return (
    <section className="py-20 bg-luxury-cream">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-luxury-serif text-4xl md:text-5xl font-bold text-luxury-black mb-4">
            Our Collections
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover our carefully curated selection of luxury timepieces and fine jewelry
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8">
          {categories.map((category, index) => (
            <motion.div
              key={category.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Link
                href={category.href}
                className="group block relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500"
              >
                <div className="aspect-[4/3] relative overflow-hidden">
                  {/* Background Image */}
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                    onError={(e) => {
                      // Fallback to gradient background if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />

                  {/* Fallback gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-luxury-black to-luxury-charcoal">
                    <div className="absolute inset-0 opacity-10">
                      <div className="w-full h-full flex items-center justify-center">
                        <category.icon className="w-32 h-32 text-luxury-gold" />
                      </div>
                    </div>
                  </div>

                  {/* Dark overlay for better text readability */}
                  <div className="absolute inset-0 bg-black/40"></div>
                  
                  {/* Content */}
                  <div className="absolute inset-0 p-8 flex flex-col justify-end">
                    <div className="text-white">
                      <category.icon className="w-8 h-8 text-luxury-gold mb-4" />
                      <h3 className="text-2xl font-bold mb-2">{category.name}</h3>
                      <p className="text-gray-300 mb-4">{category.description}</p>
                      <div className="inline-flex items-center gap-2 text-luxury-gold font-semibold group-hover:gap-3 transition-all">
                        Explore Collection
                        <ArrowRight className="w-4 h-4" />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
