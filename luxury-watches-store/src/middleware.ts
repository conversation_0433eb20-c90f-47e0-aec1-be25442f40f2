import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Check if the request is for admin routes
  if (request.nextUrl.pathname.startsWith('/admin')) {
    // For now, we'll allow access to admin routes
    // In production, you would check for authentication here
    
    // Example authentication check (commented out for development):
    /*
    const token = request.cookies.get('admin-token')
    
    if (!token) {
      // Redirect to admin login page
      return NextResponse.redirect(new URL('/admin/login', request.url))
    }
    
    // Verify token validity
    try {
      // Verify JWT token or session
      // const isValid = await verifyAdminToken(token.value)
      // if (!isValid) {
      //   return NextResponse.redirect(new URL('/admin/login', request.url))
      // }
    } catch (error) {
      return NextResponse.redirect(new URL('/admin/login', request.url))
    }
    */
  }

  // Check if the request is for Sanity Studio
  if (request.nextUrl.pathname.startsWith('/studio')) {
    // Allow access to Sanity Studio
    return NextResponse.next()
  }

  // Add security headers for all requests
  const response = NextResponse.next()
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=()'
  )

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
