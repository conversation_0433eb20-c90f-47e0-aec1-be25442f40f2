import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight, Calendar, MapPin, Award } from 'lucide-react'
import { getAllBrands } from '@/lib/sanity/utils'
import { BrandsPageClient } from './brands-client'

export const metadata: Metadata = {
  title: 'Luxury Watch Brands - Luxury Watches Store',
  description: 'Discover the world\'s most prestigious luxury watch brands, their heritage, and iconic timepieces.',
}

const brandImages = {
  'rolex': 'https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?w=600&h=400&fit=crop&crop=center',
  'patek-philippe': 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=600&h=400&fit=crop&crop=center',
  'audemars-piguet': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=400&fit=crop&crop=center',
  'omega': 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=600&h=400&fit=crop&crop=center',
  'cartier': 'https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=600&h=400&fit=crop&crop=center',
  'breitling': 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=600&h=400&fit=crop&crop=center'
}

export default async function BrandsPage() {
  const brands = await getAllBrands()

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-luxury-cream to-luxury-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold text-luxury-black mb-6">
              Prestigious <span className="text-luxury-gold">Brands</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Explore the heritage and craftsmanship of the world's most renowned luxury watch and jewelry manufacturers
            </p>
          </div>
        </div>
      </section>

      {/* Brands Component */}
      <BrandsPageClient brands={brands} />

      {/* Call to Action */}
      <section className="py-20 bg-luxury-cream">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-6">
            Discover Your Perfect Timepiece
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Browse our curated collection of luxury watches and jewelry from these prestigious brands
          </p>
          <Link
            href="/catalog"
            className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
          >
            View All Products
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </section>
    </div>
  )
}
