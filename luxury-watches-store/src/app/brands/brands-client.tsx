'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight, Calendar, MapPin, Award } from 'lucide-react'

interface Brand {
  _id: string
  name: string
  slug: { current: string }
  description: string
  founded: number
  country: string
  website?: string
  isLuxury: boolean
}

interface BrandsPageClientProps {
  brands: Brand[]
}

const brandImages = {
  'rolex': 'https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?w=600&h=400&fit=crop&crop=center',
  'patek-philippe': 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=600&h=400&fit=crop&crop=center',
  'audemars-piguet': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=400&fit=crop&crop=center',
  'omega': 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=600&h=400&fit=crop&crop=center',
  'cartier': 'https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=600&h=400&fit=crop&crop=center',
  'tag-heuer': 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=600&h=400&fit=crop&crop=center',
  'bulgari': 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=600&h=400&fit=crop&crop=center',
  'van-cleef-arpels': 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=600&h=400&fit=crop&crop=center'
}

export function BrandsPageClient({ brands }: BrandsPageClientProps) {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {brands.map((brand, index) => (
            <motion.div
              key={brand._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden group"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={brandImages[brand.slug.current as keyof typeof brandImages] || brandImages.rolex}
                  alt={`${brand.name} luxury products`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                <div className="absolute bottom-4 left-4">
                  <h3 className="font-luxury-serif text-2xl font-bold text-white">
                    {brand.name}
                  </h3>
                </div>
              </div>
              
              <div className="p-6">
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {brand.description}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Founded {brand.founded}
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {brand.country}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 mb-4">
                  <Award className="w-4 h-4 text-luxury-gold" />
                  <span className="text-sm text-luxury-gold font-medium">
                    {brand.isLuxury ? 'Luxury Heritage Brand' : 'Premium Brand'}
                  </span>
                </div>
                
                <Link
                  href={`/brands/${brand.slug.current}`}
                  className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark font-semibold group-hover:gap-3 transition-all"
                >
                  Explore Brand
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
        
        {brands.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              No brands found. Please check back later.
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
