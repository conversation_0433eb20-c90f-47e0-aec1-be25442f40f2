import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the incoming data
    const {
      name,
      value,
      id,
      label,
      delta,
      rating,
      navigationType,
      timestamp,
      url,
      userAgent
    } = body

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Web Vitals Metric:', {
        name,
        value: Math.round(value),
        rating,
        url: new URL(url).pathname
      })
    }

    // In production, you would send this data to your analytics service
    // Examples:
    // - Google Analytics
    // - DataDog
    // - New Relic
    // - Custom analytics database
    
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to external analytics service
      // await sendToAnalyticsService({
      //   metric: name,
      //   value,
      //   page: new URL(url).pathname,
      //   timestamp,
      //   userAgent,
      //   rating
      // })
      
      // Example: Store in database
      // await storeWebVital({
      //   name,
      //   value,
      //   url,
      //   timestamp,
      //   userAgent,
      //   rating
      // })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error processing web vitals:', error)
    return NextResponse.json(
      { error: 'Failed to process web vitals' },
      { status: 500 }
    )
  }
}

// Helper function to categorize performance
function categorizePerformance(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const thresholds = {
    CLS: { good: 0.1, poor: 0.25 },
    FID: { good: 100, poor: 300 },
    FCP: { good: 1800, poor: 3000 },
    LCP: { good: 2500, poor: 4000 },
    TTFB: { good: 800, poor: 1800 },
    INP: { good: 200, poor: 500 }
  }

  const threshold = thresholds[name as keyof typeof thresholds]
  if (!threshold) return 'good'

  if (value <= threshold.good) return 'good'
  if (value <= threshold.poor) return 'needs-improvement'
  return 'poor'
}

// Example function to send to external analytics service
async function sendToAnalyticsService(data: any) {
  // Implementation depends on your analytics provider
  // Example for a custom service:
  /*
  const response = await fetch('https://your-analytics-api.com/metrics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`
    },
    body: JSON.stringify(data)
  })
  
  if (!response.ok) {
    throw new Error('Failed to send to analytics service')
  }
  */
}

// Example function to store in database
async function storeWebVital(data: any) {
  // Implementation depends on your database
  // Example with Prisma:
  /*
  await prisma.webVital.create({
    data: {
      name: data.name,
      value: data.value,
      url: data.url,
      timestamp: new Date(data.timestamp),
      userAgent: data.userAgent,
      rating: data.rating
    }
  })
  */
}
