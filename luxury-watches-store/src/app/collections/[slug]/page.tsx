'use client'

import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ArrowLeft, ArrowRight, Star, Award } from 'lucide-react'
import { NextSeo } from 'next-seo'
import { WatchCard } from '@/components/product/watch-card'
import { WATCH_COLLECTIONS } from '@/lib/constants'
import { getProductsByCollection } from '@/lib/products'

interface CollectionPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function CollectionPage({ params }: CollectionPageProps) {
  const { slug } = await params
  const collection = WATCH_COLLECTIONS.find(c => c.slug === slug)
  
  if (!collection) {
    notFound()
  }

  const products = getProductsByCollection(slug)

  return (
    <>
      <NextSeo
        title={`${collection.name} - Luxury Watches Store`}
        description={collection.description}
        openGraph={{
          title: `${collection.name} - Luxury Watches Store`,
          description: collection.description,
          type: 'website',
        }}
      />
      
      <div className="min-h-screen bg-luxury-white">
        {/* Back Navigation */}
        <div className="container mx-auto px-4 pt-8">
          <Link
            href="/collections"
            className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Collections
          </Link>
        </div>

        {/* Hero Section */}
        <section className="bg-gradient-to-br from-luxury-cream to-luxury-white py-20">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold text-luxury-black mb-6">
                  {collection.name}
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed mb-8">
                  {collection.description}
                </p>
                <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-luxury-gold" />
                    <span>{products.length} timepieces</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-luxury-gold" />
                    <span>Curated Collection</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            {products.length === 0 ? (
              <div className="text-center py-20">
                <h2 className="font-luxury-serif text-3xl font-semibold text-luxury-black mb-4">
                  Coming Soon
                </h2>
                <p className="text-gray-600 mb-8">
                  We're curating exceptional timepieces for this collection. Check back soon!
                </p>
                <Link
                  href="/catalog"
                  className="inline-flex items-center px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
                >
                  Explore All Watches
                </Link>
              </div>
            ) : (
              <>
                <div className="text-center mb-16">
                  <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
                    Featured Timepieces
                  </h2>
                  <p className="text-xl text-gray-600">
                    Discover the exceptional watches in our {collection.name.toLowerCase()} collection
                  </p>
                </div>

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
                >
                  {products.map((product, index) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                    >
                      <WatchCard watch={product} viewMode="grid" />
                    </motion.div>
                  ))}
                </motion.div>
              </>
            )}
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 bg-luxury-cream">
          <div className="container mx-auto px-4 text-center">
            <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-6">
              Explore More Collections
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Discover other carefully curated collections of luxury timepieces
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/collections"
                className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
              >
                View All Collections
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/catalog"
                className="inline-flex items-center gap-2 px-8 py-4 border-2 border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-colors"
              >
                Browse Catalog
              </Link>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
