'use client'

import { useState } from 'react'
import { Metadata } from 'next'
import { MapPin, Phone, Mail, Clock, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'

const contactInfo = [
  {
    icon: MapPin,
    title: 'Visit Our Showroom',
    details: ['123 Luxury Avenue', 'Beverly Hills, CA 90210', 'United States'],
    action: 'Get Directions'
  },
  {
    icon: Phone,
    title: 'Call Us',
    details: ['+****************', 'Toll-free: 1-800-LUXURY'],
    action: 'Call Now'
  },
  {
    icon: Mail,
    title: 'Email Us',
    details: ['<EMAIL>', '<EMAIL>'],
    action: 'Send Email'
  },
  {
    icon: Clock,
    title: 'Business Hours',
    details: ['Monday - Friday: 9:00 AM - 7:00 PM', 'Saturday: 10:00 AM - 6:00 PM', 'Sunday: 12:00 PM - 5:00 PM'],
    action: 'View Calendar'
  }
]

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    interest: 'general'
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('Thank you for your message! We\'ll get back to you within 24 hours.')
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      interest: 'general'
    })
    setIsSubmitting(false)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-luxury-cream to-luxury-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold text-luxury-black mb-6">
              Contact <span className="text-luxury-gold">Us</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Get in touch with our luxury watch experts for personalized service and expert guidance
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
            {contactInfo.map((info, index) => {
              const Icon = info.icon
              return (
                <div
                  key={index}
                  className="text-center bg-white p-8 rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300"
                >
                  <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mx-auto mb-6">
                    <Icon className="w-8 h-8 text-luxury-black" />
                  </div>
                  <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-4">
                    {info.title}
                  </h3>
                  <div className="space-y-2 mb-6">
                    {info.details.map((detail, idx) => (
                      <p key={idx} className="text-gray-600">
                        {detail}
                      </p>
                    ))}
                  </div>
                  <button className="text-luxury-gold hover:text-luxury-gold-dark font-semibold transition-colors">
                    {info.action}
                  </button>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 bg-luxury-cream">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
                Send Us a Message
              </h2>
              <p className="text-xl text-gray-600">
                Have a question about our timepieces? We're here to help.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-luxury p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-semibold text-luxury-black mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent transition-colors"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-luxury-black mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-semibold text-luxury-black mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent transition-colors"
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <label htmlFor="interest" className="block text-sm font-semibold text-luxury-black mb-2">
                      I'm Interested In
                    </label>
                    <select
                      id="interest"
                      name="interest"
                      value={formData.interest}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent transition-colors"
                    >
                      <option value="general">General Inquiry</option>
                      <option value="purchase">Purchasing a Watch</option>
                      <option value="service">Watch Service</option>
                      <option value="authentication">Authentication</option>
                      <option value="investment">Investment Advice</option>
                      <option value="custom">Custom Request</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-semibold text-luxury-black mb-2">
                    Subject *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent transition-colors"
                    placeholder="How can we help you?"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-semibold text-luxury-black mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent transition-colors resize-none"
                    placeholder="Please provide details about your inquiry..."
                  />
                </div>

                <div className="text-center">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-luxury-black border-t-transparent rounded-full animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Send Message
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Quick answers to common questions
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "Do you offer authentication services?",
                  answer: "Yes, we provide professional authentication services for luxury watches. Our certified experts can verify the authenticity of your timepiece."
                },
                {
                  question: "What is your return policy?",
                  answer: "We offer a 30-day return policy for all purchases. Items must be in original condition with all documentation and packaging."
                },
                {
                  question: "Do you provide warranty coverage?",
                  answer: "All our watches come with manufacturer warranty when applicable, plus our own service guarantee for peace of mind."
                },
                {
                  question: "Can you help with watch servicing?",
                  answer: "Absolutely! Our master watchmaker provides comprehensive servicing for all luxury watch brands in our climate-controlled workshop."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-white rounded-lg shadow-luxury p-6">
                  <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
