'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  User, 
  Heart, 
  ShoppingBag, 
  MapPin, 
  Settings, 
  LogOut,
  Mail,
  Phone,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useCart } from '@/contexts/cart-context';
import { formatPrice } from '@/lib/utils';

export default function AccountPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const { itemCount: wishlistCount } = useWishlist();
  const { itemCount: cartCount, total: cartTotal } = useCart();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-luxury-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your account...</p>
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  const accountStats = [
    {
      icon: Heart,
      label: 'Wishlist Items',
      value: wishlistCount,
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      href: '/account/wishlist'
    },
    {
      icon: ShoppingBag,
      label: 'Cart Items',
      value: cartCount,
      color: 'text-luxury-gold',
      bgColor: 'bg-luxury-gold/10',
      href: '/cart'
    },
    {
      icon: MapPin,
      label: 'Saved Addresses',
      value: 2, // Mock data
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      href: '/account/addresses'
    }
  ];

  const quickActions = [
    {
      icon: Heart,
      label: 'My Wishlist',
      description: 'View your saved timepieces',
      href: '/account/wishlist',
      color: 'text-red-500'
    },
    {
      icon: ShoppingBag,
      label: 'Order History',
      description: 'Track your purchases',
      href: '/account/orders',
      color: 'text-green-500'
    },
    {
      icon: MapPin,
      label: 'Addresses',
      description: 'Manage shipping addresses',
      href: '/account/addresses',
      color: 'text-blue-500'
    },
    {
      icon: Settings,
      label: 'Account Settings',
      description: 'Update your preferences',
      href: '/account/settings',
      color: 'text-gray-500'
    }
  ];

  return (
    <div className="min-h-screen bg-luxury-cream py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl shadow-luxury p-8 mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center space-x-6">
              {/* Avatar */}
              <div className="relative">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={`${user.firstName} ${user.lastName}`}
                    className="w-20 h-20 rounded-full object-cover border-4 border-luxury-gold"
                  />
                ) : (
                  <div className="w-20 h-20 bg-luxury-gold rounded-full flex items-center justify-center">
                    <User className="w-10 h-10 text-luxury-black" />
                  </div>
                )}
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
              </div>

              {/* User Info */}
              <div>
                <h1 className="text-3xl font-luxury-serif font-bold text-luxury-black">
                  {user.firstName} {user.lastName}
                </h1>
                <div className="flex items-center space-x-4 mt-2 text-gray-600">
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    <span className="text-sm">{user.email}</span>
                  </div>
                  {user.phone && (
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="text-sm">{user.phone}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center mt-1 text-gray-500">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span className="text-sm">
                    Member since {new Date(user.createdAt).toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long' 
                    })}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="mt-6 md:mt-0 flex space-x-3">
              <Button variant="outline" onClick={() => router.push('/account/settings')}>
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          {accountStats.map((stat, index) => (
            <div
              key={stat.label}
              className="bg-white rounded-xl shadow-luxury p-6 hover:shadow-luxury-hover transition-shadow cursor-pointer"
              onClick={() => router.push(stat.href)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-3xl font-bold text-luxury-black mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </motion.div>

        {/* Current Cart Summary */}
        {cartCount > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-luxury-gold/10 border border-luxury-gold/20 rounded-xl p-6 mb-8"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-luxury-black">Current Cart</h3>
                <p className="text-gray-600">
                  {cartCount} item{cartCount !== 1 ? 's' : ''} • Total: {formatPrice(cartTotal)}
                </p>
              </div>
              <Button 
                variant="luxury" 
                onClick={() => router.push('/cart')}
              >
                View Cart
              </Button>
            </div>
          </motion.div>
        )}

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-2xl shadow-luxury p-8"
        >
          <h2 className="text-2xl font-luxury-serif font-bold text-luxury-black mb-6">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.label}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-luxury-gold hover:shadow-md transition-all cursor-pointer group"
                onClick={() => router.push(action.href)}
              >
                <div className={`p-3 rounded-full bg-gray-50 group-hover:bg-luxury-cream transition-colors`}>
                  <action.icon className={`w-6 h-6 ${action.color}`} />
                </div>
                <div className="ml-4">
                  <h3 className="font-semibold text-luxury-black group-hover:text-luxury-gold transition-colors">
                    {action.label}
                  </h3>
                  <p className="text-sm text-gray-600">{action.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
