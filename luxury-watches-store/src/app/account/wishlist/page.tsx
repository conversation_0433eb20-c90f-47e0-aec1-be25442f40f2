'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Heart, ShoppingBag, Trash2, ArrowLeft, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useCart } from '@/contexts/cart-context';
import { formatPrice } from '@/lib/utils';

export default function WishlistPage() {
  const { isAuthenticated } = useAuth();
  const { items, removeItem, clearWishlist } = useWishlist();
  const { addItem: addToCart, openCart } = useCart();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-luxury-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your wishlist...</p>
        </div>
      </div>
    );
  }

  const handleAddToCart = (watch: any) => {
    addToCart(watch, 1);
    openCart();
  };

  const handleRemoveFromWishlist = (watchId: string) => {
    removeItem(watchId);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'My Luxury Watch Wishlist',
        text: 'Check out my luxury watch wishlist',
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <div className="min-h-screen bg-luxury-cream py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center mb-4">
            <Link href="/account">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Account
              </Button>
            </Link>
          </div>
          
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-4xl font-luxury-serif font-bold text-luxury-black mb-2">
                My Wishlist
              </h1>
              <p className="text-gray-600">
                {items.length} {items.length === 1 ? 'timepiece' : 'timepieces'} saved for later
              </p>
            </div>
            
            {items.length > 0 && (
              <div className="mt-4 md:mt-0 flex space-x-3">
                <Button variant="outline" onClick={handleShare}>
                  <Share2 className="w-4 h-4 mr-2" />
                  Share Wishlist
                </Button>
                <Button 
                  variant="outline" 
                  onClick={clearWishlist}
                  className="text-red-600 hover:text-red-700 hover:border-red-300"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear All
                </Button>
              </div>
            )}
          </div>
        </motion.div>

        {/* Wishlist Content */}
        {items.length === 0 ? (
          /* Empty State */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-luxury p-12 text-center"
          >
            <div className="w-24 h-24 bg-luxury-cream rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="w-12 h-12 text-gray-400" />
            </div>
            <h2 className="text-2xl font-luxury-serif font-bold text-luxury-black mb-4">
              Your wishlist is empty
            </h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start building your dream collection by adding luxury timepieces to your wishlist.
            </p>
            <Link href="/catalog">
              <Button variant="luxury" size="lg">
                Explore Watches
              </Button>
            </Link>
          </motion.div>
        ) : (
          /* Wishlist Items */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {items.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-luxury overflow-hidden hover:shadow-luxury-hover transition-shadow group"
              >
                {/* Image */}
                <div className="relative h-64 bg-luxury-cream">
                  <img
                    src={item.watch.image}
                    alt={`${item.watch.brand} ${item.watch.name}`}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    {item.watch.isNew && (
                      <span className="bg-luxury-gold text-luxury-black px-2 py-1 rounded-full text-xs font-semibold">
                        NEW
                      </span>
                    )}
                    {item.watch.isBestseller && (
                      <span className="bg-luxury-charcoal text-white px-2 py-1 rounded-full text-xs font-semibold">
                        BESTSELLER
                      </span>
                    )}
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={() => handleRemoveFromWishlist(item.watchId)}
                    className="absolute top-4 right-4 p-2 bg-white/90 hover:bg-white rounded-full shadow-md transition-colors group-hover:opacity-100 opacity-0"
                  >
                    <Trash2 className="w-4 h-4 text-red-500" />
                  </button>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="mb-4">
                    <p className="text-luxury-gold text-sm font-medium">{item.watch.brand}</p>
                    <h3 className="font-luxury-serif text-lg font-semibold text-luxury-black line-clamp-2">
                      {item.watch.name}
                    </h3>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {item.watch.description}
                  </p>

                  {/* Price */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      {item.watch.originalPrice && (
                        <span className="text-gray-400 line-through text-sm">
                          {formatPrice(item.watch.originalPrice)}
                        </span>
                      )}
                      <span className="text-luxury-gold font-bold text-lg">
                        {formatPrice(item.watch.price)}
                      </span>
                    </div>
                  </div>

                  {/* Added Date */}
                  <p className="text-xs text-gray-500 mb-4">
                    Added {new Date(item.addedAt).toLocaleDateString()}
                  </p>

                  {/* Actions */}
                  <div className="flex space-x-3">
                    <Button
                      onClick={() => handleAddToCart(item.watch)}
                      disabled={!item.watch.inStock}
                      className="flex-1"
                      variant={item.watch.inStock ? "luxury" : "secondary"}
                    >
                      <ShoppingBag className="w-4 h-4 mr-2" />
                      {item.watch.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </Button>
                    <Link href={`/catalog/${item.watchId}`}>
                      <Button variant="outline" size="icon">
                        <ArrowLeft className="w-4 h-4 rotate-180" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Continue Shopping */}
        {items.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-center mt-12"
          >
            <Link href="/catalog">
              <Button variant="outline" size="lg">
                Continue Shopping
              </Button>
            </Link>
          </motion.div>
        )}
      </div>
    </div>
  );
}
