import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Playfair_Display } from 'next/font/google';
import "./globals.css";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { CartProvider } from "@/contexts/cart-context";
import { AuthProvider } from "@/contexts/auth-context";
import { WishlistProvider } from "@/contexts/wishlist-context";
import { ComparisonProvider } from "@/contexts/comparison-context";
import { ToastProvider } from "@/components/ui/toast";
import { CartSidebar } from "@/components/cart/cart-sidebar";
import { ComparisonSidebar } from "@/components/product/comparison-sidebar";
import { GoogleAnalytics } from "@/components/analytics/google-analytics";
import { WebVitals } from "@/components/analytics/web-vitals";
import { OrganizationStructuredData, WebsiteStructuredData } from "@/components/seo/structured-data";
import { SITE_CONFIG } from "@/lib/constants";

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const playfairDisplay = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair-display',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: SITE_CONFIG.name,
    template: `%s | ${SITE_CONFIG.name}`,
  },
  description: SITE_CONFIG.description,
  keywords: [
    'luxury watches',
    'premium timepieces',
    'Swiss watches',
    'Rolex',
    'Patek Philippe',
    'Audemars Piguet',
    'watch collection',
    'luxury accessories',
    'investment watches',
    'vintage watches',
    'watch authentication',
    'luxury watch store',
    'certified pre-owned watches',
    'horological excellence'
  ],
  authors: [
    {
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
    },
  ],
  creator: SITE_CONFIG.name,
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: SITE_CONFIG.url,
    title: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
    siteName: SITE_CONFIG.name,
    images: [
      {
        url: SITE_CONFIG.ogImage,
        width: 1200,
        height: 630,
        alt: SITE_CONFIG.name,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
    images: [SITE_CONFIG.ogImage],
    creator: '@luxurytimepieces',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({
  children
}: RootLayoutProps) {
  return (
    <html lang="en" className={`${inter.variable} ${playfairDisplay.variable}`}>
      <head>
        <OrganizationStructuredData />
        <WebsiteStructuredData />
      </head>
      <body className="min-h-screen bg-luxury-white font-luxury-sans antialiased">
        <GoogleAnalytics />
        <WebVitals />
        <ToastProvider>
          <AuthProvider>
            <WishlistProvider>
              <ComparisonProvider>
                <CartProvider>
                  <div className="flex flex-col min-h-screen">
                    <Header />
                    <main className="flex-1">
                      {children}
                    </main>
                    <Footer />
                  </div>
                  <CartSidebar />
                  <ComparisonSidebar />
                </CartProvider>
              </ComparisonProvider>
            </WishlistProvider>
          </AuthProvider>
        </ToastProvider>
      </body>
    </html>
  );
}
