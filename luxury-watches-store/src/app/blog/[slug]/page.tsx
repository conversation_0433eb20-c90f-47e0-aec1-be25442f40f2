import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, User, ArrowLeft, Tag } from 'lucide-react'
import { getBlogPostBySlug, getImageUrl } from '@/lib/sanity/utils'
import { PortableTextRenderer } from '@/components/sanity/portable-text'
import { BlogPostStructuredData, BreadcrumbStructuredData } from '@/components/seo/structured-data'

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params
  const post = await getBlogPostBySlug(slug)
  
  if (!post) {
    return {
      title: 'Post Not Found - Luxury Watches Store'
    }
  }

  return {
    title: `${post.title} - Luxury Watches Store`,
    description: post.excerpt || post.seo?.description,
    keywords: post.seo?.keywords,
    openGraph: {
      title: post.seo?.title || post.title,
      description: post.excerpt || post.seo?.description,
      images: post.featuredImage ? [getImageUrl(post.featuredImage, 1200, 630)] : [],
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPostBySlug(slug)

  if (!post) {
    notFound()
  }

  // Breadcrumb data for structured data
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Blog', url: '/blog' },
    { name: post.title, url: `/blog/${slug}` }
  ];

  return (
    <div className="min-h-screen bg-luxury-white">
      <BlogPostStructuredData post={post} />
      <BreadcrumbStructuredData items={breadcrumbItems} />

      {/* Back Navigation */}
      <div className="container mx-auto px-4 pt-8">
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Blog
        </Link>
      </div>

      {/* Article Header */}
      <article className="container mx-auto px-4 py-12">
        <header className="max-w-4xl mx-auto text-center mb-12">
          <h1 className="font-luxury-serif text-4xl md:text-5xl font-bold text-luxury-black mb-6">
            {post.title}
          </h1>
          
          <div className="flex items-center justify-center gap-6 text-gray-600 mb-8">
            {post.publishedAt && (
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                {new Date(post.publishedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            )}
            {post.author && (
              <div className="flex items-center gap-2">
                <User className="w-5 h-5" />
                {post.author}
              </div>
            )}
          </div>

          {post.categories && post.categories.length > 0 && (
            <div className="flex items-center justify-center gap-2 mb-8">
              <Tag className="w-4 h-4 text-gray-400" />
              <div className="flex gap-2">
                {post.categories.map((category: string) => (
                  <span
                    key={category}
                    className="px-3 py-1 bg-luxury-cream text-luxury-black text-sm rounded-full"
                  >
                    {category}
                  </span>
                ))}
              </div>
            </div>
          )}
        </header>

        {/* Featured Image */}
        {post.featuredImage && (
          <div className="max-w-4xl mx-auto mb-12">
            <div className="relative h-96 md:h-[500px] rounded-xl overflow-hidden shadow-luxury">
              <Image
                src={getImageUrl(post.featuredImage, 1200, 600)}
                alt={post.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        )}

        {/* Article Content */}
        <div className="max-w-3xl mx-auto">
          {post.content && (
            <PortableTextRenderer 
              content={post.content}
              className="prose-luxury"
            />
          )}
        </div>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="max-w-3xl mx-auto mt-12 pt-8 border-t border-gray-200">
            <h3 className="font-semibold text-luxury-black mb-4">Tags:</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag: string) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-luxury-cream text-luxury-black text-sm rounded-full hover:bg-luxury-gold hover:text-luxury-black transition-colors cursor-pointer"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="max-w-3xl mx-auto mt-12 pt-8 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <Link
              href="/blog"
              className="inline-flex items-center gap-2 px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              All Posts
            </Link>
            
            <Link
              href="/catalog"
              className="inline-flex items-center gap-2 px-6 py-3 border-2 border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-colors"
            >
              Shop Watches
            </Link>
          </div>
        </div>
      </article>
    </div>
  )
}
