import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, User, Tag } from 'lucide-react'
import { getAllBlogPosts, getImageUrl } from '@/lib/sanity/utils'

export const metadata: Metadata = {
  title: 'Blog - Luxury Watches Store',
  description: 'Discover the latest news, reviews, and insights from the world of luxury watches.',
}

export default async function BlogPage() {
  const posts = await getAllBlogPosts()

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-luxury-cream to-luxury-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold text-luxury-black mb-6">
              Watch <span className="text-luxury-gold">Journal</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Discover the latest news, reviews, and insights from the world of luxury timepieces
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          {posts.length === 0 ? (
            <div className="text-center py-20">
              <h2 className="font-luxury-serif text-3xl font-semibold text-luxury-black mb-4">
                Coming Soon
              </h2>
              <p className="text-gray-600 mb-8">
                We're preparing exciting content about luxury watches. Check back soon!
              </p>
              <Link
                href="/catalog"
                className="inline-flex items-center px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
              >
                Explore Our Collection
              </Link>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post: any) => (
                <article
                  key={post._id}
                  className="bg-white rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden"
                >
                  {post.featuredImage && (
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={getImageUrl(post.featuredImage, 400, 300)}
                        alt={post.title}
                        fill
                        className="object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}
                  
                  <div className="p-6">
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      {post.publishedAt && (
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {new Date(post.publishedAt).toLocaleDateString()}
                        </div>
                      )}
                      {post.author && (
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          {post.author}
                        </div>
                      )}
                    </div>
                    
                    <h2 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-3 line-clamp-2">
                      {post.title}
                    </h2>
                    
                    {post.excerpt && (
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                    )}
                    
                    {post.categories && post.categories.length > 0 && (
                      <div className="flex items-center gap-2 mb-4">
                        <Tag className="w-4 h-4 text-gray-400" />
                        <div className="flex gap-2">
                          {post.categories.slice(0, 2).map((category: string) => (
                            <span
                              key={category}
                              className="px-2 py-1 bg-luxury-cream text-luxury-black text-xs rounded-full"
                            >
                              {category}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <Link
                      href={`/blog/${post.slug.current}`}
                      className="inline-flex items-center text-luxury-gold hover:text-luxury-gold-dark font-semibold"
                    >
                      Read More →
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
}
