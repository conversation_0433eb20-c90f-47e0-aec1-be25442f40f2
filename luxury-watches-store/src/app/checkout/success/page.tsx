'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  Package, 
  Truck, 
  Mail, 
  Calendar,
  Download,
  ArrowRight,
  Star
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatPrice } from '@/lib/utils';

// Mock order data - in real app this would come from API/database
const mockOrder = {
  id: 'ORD-2024-001234',
  date: new Date().toLocaleDateString(),
  total: 18500,
  items: [
    {
      id: '1',
      name: 'Submariner Date "Hulk"',
      brand: 'Rolex',
      price: 18500,
      quantity: 1,
      image: '/images/watches/rolex-submariner-1.webp'
    }
  ],
  shipping: {
    method: 'Express Delivery',
    estimatedDelivery: '3-5 business days',
    trackingNumber: 'LUX123456789'
  },
  customer: {
    email: '<EMAIL>',
    name: '<PERSON>'
  }
};

export default function CheckoutSuccessPage() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <div className="min-h-screen bg-luxury-cream py-8">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          {/* Success Header */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
              className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6"
            >
              <CheckCircle className="h-10 w-10 text-green-600" />
            </motion.div>
            
            <h1 className="font-luxury-serif text-4xl md:text-5xl font-bold text-luxury-black mb-4">
              Order Confirmed!
            </h1>
            <p className="text-xl text-gray-600 mb-2">
              Thank you for your luxury timepiece purchase
            </p>
            <p className="text-gray-500">
              Order #{mockOrder.id} • Placed on {mockOrder.date}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Order Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg shadow-luxury p-6"
              >
                <h2 className="font-luxury-serif text-2xl font-semibold text-luxury-black mb-6">
                  Order Details
                </h2>
                
                <div className="space-y-4">
                  {mockOrder.items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-4 bg-luxury-cream rounded-lg">
                      <div className="w-20 h-20 bg-white rounded-lg flex items-center justify-center">
                        <span className="text-luxury-gold text-3xl">⌚</span>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-luxury-black">
                          {item.brand} {item.name}
                        </h3>
                        <p className="text-gray-600">Quantity: {item.quantity}</p>
                        <p className="text-luxury-gold font-bold text-lg">
                          {formatPrice(item.price)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-lg">Total Paid</span>
                    <span className="font-bold text-2xl text-luxury-gold">
                      {formatPrice(mockOrder.total)}
                    </span>
                  </div>
                </div>
              </motion.div>

              {/* Shipping Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-white rounded-lg shadow-luxury p-6"
              >
                <h2 className="font-luxury-serif text-2xl font-semibold text-luxury-black mb-6">
                  Shipping & Delivery
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-luxury-gold rounded-full flex items-center justify-center">
                        <Package className="h-5 w-5 text-luxury-black" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">Processing</h3>
                        <p className="text-sm text-gray-600">Your order is being prepared</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <Truck className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-500">Shipping</h3>
                        <p className="text-sm text-gray-400">Will be shipped soon</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900">Delivery Method</h4>
                      <p className="text-gray-600">{mockOrder.shipping.method}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Estimated Delivery</h4>
                      <p className="text-gray-600">{mockOrder.shipping.estimatedDelivery}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Tracking Number</h4>
                      <p className="text-luxury-gold font-mono">{mockOrder.shipping.trackingNumber}</p>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* What's Next */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="bg-white rounded-lg shadow-luxury p-6"
              >
                <h2 className="font-luxury-serif text-2xl font-semibold text-luxury-black mb-6">
                  What's Next?
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-luxury-cream rounded-full flex items-center justify-center mx-auto mb-3">
                      <Mail className="h-6 w-6 text-luxury-gold" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">Email Confirmation</h3>
                    <p className="text-sm text-gray-600">
                      We've sent a confirmation email to {mockOrder.customer.email}
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-luxury-cream rounded-full flex items-center justify-center mx-auto mb-3">
                      <Package className="h-6 w-6 text-luxury-gold" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">Order Processing</h3>
                    <p className="text-sm text-gray-600">
                      Your order will be processed within 1-2 business days
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-luxury-cream rounded-full flex items-center justify-center mx-auto mb-3">
                      <Truck className="h-6 w-6 text-luxury-gold" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">Shipping Updates</h3>
                    <p className="text-sm text-gray-600">
                      You'll receive tracking information once shipped
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              {/* Quick Actions */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
                className="bg-white rounded-lg shadow-luxury p-6"
              >
                <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-4">
                  Quick Actions
                </h3>
                
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/account/orders">
                      <Calendar className="h-4 w-4 mr-2" />
                      View Order History
                    </Link>
                  </Button>
                  
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="h-4 w-4 mr-2" />
                    Download Invoice
                  </Button>
                  
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/catalog">
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Continue Shopping
                    </Link>
                  </Button>
                </div>
              </motion.div>

              {/* Customer Support */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
                className="bg-luxury-charcoal text-white rounded-lg shadow-luxury p-6"
              >
                <h3 className="font-luxury-serif text-xl font-semibold mb-4">
                  Need Help?
                </h3>
                
                <p className="text-gray-300 mb-4">
                  Our luxury timepiece specialists are here to assist you.
                </p>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-luxury-gold" />
                    <span className="text-sm"><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-luxury-gold">📞</span>
                    <span className="text-sm">+1 (555) 123-4567</span>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full mt-4 border-luxury-gold text-luxury-gold hover:bg-luxury-gold hover:text-luxury-black">
                  Contact Support
                </Button>
              </motion.div>

              {/* Review Reminder */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 }}
                className="bg-luxury-cream rounded-lg p-6"
              >
                <div className="text-center">
                  <Star className="h-8 w-8 text-luxury-gold mx-auto mb-3" />
                  <h3 className="font-semibold text-luxury-black mb-2">
                    Love Your Purchase?
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Share your experience and help other luxury watch enthusiasts.
                  </p>
                  <Button variant="luxury" size="sm" className="w-full">
                    Write a Review
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Return to Home */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="text-center mt-12"
          >
            <Button variant="luxury" size="lg" asChild>
              <Link href="/">
                Return to Homepage
              </Link>
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
