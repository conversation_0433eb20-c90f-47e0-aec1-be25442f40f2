'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Eye, EyeOff, Mail, Lock, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';
import { cn } from '@/lib/utils';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  
  const { login, isLoading, error, isAuthenticated, user, clearError } = useAuth();
  const router = useRouter();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      setTimeout(() => {
        router.push('/account');
      }, 100);
    }
  }, [isAuthenticated, user, router]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      if (clearError) clearError();
    };
  }, [clearError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      alert('Please fill in both email and password');
      return;
    }

    try {
      await login({ email, password });
      // Login success will be handled by the auth context and redirect
    } catch (err) {
      console.error('Login error:', err);
    }
  };

  const handleDemoLogin = () => {
    setEmail('<EMAIL>');
    setPassword('demo123');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-cream to-luxury-white flex items-center justify-center px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.3 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-luxury-gold rounded-full mb-4"
          >
            <span className="text-2xl">⌚</span>
          </motion.div>
          <h1 className="text-3xl font-luxury-serif font-bold text-luxury-black mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600">
            Sign in to your luxury watch collection
          </p>
        </div>

        {/* Demo Login Helper */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="bg-luxury-gold/10 border border-luxury-gold/20 rounded-lg p-4 mb-6"
        >
          <p className="text-sm text-luxury-black mb-2">
            <strong>Demo Account:</strong>
          </p>
          <p className="text-xs text-gray-600 mb-3">
            Email: <EMAIL><br />
            Password: demo123
          </p>
          <div className="space-y-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleDemoLogin}
              className="w-full"
              disabled={isLoading}
            >
              Fill Demo Credentials
            </Button>
            <Button
              type="button"
              variant="luxury"
              size="sm"
              onClick={async () => {
                try {
                  await login({ email: '<EMAIL>', password: 'demo123' });
                } catch (err) {
                  console.error('Direct login error:', err);
                }
              }}
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Logging in...' : 'Quick Demo Login'}
            </Button>
          </div>
        </motion.div>



        {/* Login Form */}
        <motion.form
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          onSubmit={handleSubmit}
          className="space-y-6"
        >
          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm"
            >
              {error}
            </motion.div>
          )}

          {/* Email Field */}
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium text-luxury-black">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={cn(
                  "w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-luxury-gold transition-colors",
                  "bg-white text-luxury-black placeholder-gray-400",
                  error ? "border-red-300" : "border-gray-300"
                )}
                placeholder="Enter your email"
                required
              />
            </div>
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium text-luxury-black">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={cn(
                  "w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-luxury-gold transition-colors",
                  "bg-white text-luxury-black placeholder-gray-400",
                  error ? "border-red-300" : "border-gray-300"
                )}
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>

          {/* Remember Me & Forgot Password */}
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-luxury-gold focus:ring-luxury-gold border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-600">Remember me</span>
            </label>
            <Link
              href="/auth/forgot-password"
              className="text-sm text-luxury-gold hover:text-luxury-gold-dark transition-colors"
            >
              Forgot password?
            </Link>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isLoading || !email || !password}
            className="w-full h-12 text-base"
            variant="luxury"
          >
            {isLoading ? (
              <div className="flex items-center">
                <div className="w-5 h-5 border-2 border-luxury-black border-t-transparent rounded-full animate-spin mr-2"></div>
                Signing In...
              </div>
            ) : (
              <div className="flex items-center justify-center">
                Sign In
                <ArrowRight className="ml-2 h-5 w-5" />
              </div>
            )}
          </Button>
        </motion.form>

        {/* Sign Up Link */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-8"
        >
          <p className="text-gray-600">
            Don't have an account?{' '}
            <Link
              href="/auth/register"
              className="text-luxury-gold hover:text-luxury-gold-dark font-medium transition-colors"
            >
              Create Account
            </Link>
          </p>
        </motion.div>


      </motion.div>
    </div>
  );
}
