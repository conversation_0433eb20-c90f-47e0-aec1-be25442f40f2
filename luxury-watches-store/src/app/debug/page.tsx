'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';

export default function DebugPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const { login, isAuthenticated, user, error, isLoading } = useAuth();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(message);
  };

  const testLogin = async () => {
    addLog('🧪 Starting login test...');
    try {
      await login({ email: '<EMAIL>', password: 'demo123' });
      addLog('✅ Login function completed');
    } catch (err) {
      addLog(`❌ Login error: ${err}`);
    }
  };

  const testAlert = () => {
    addLog('🔔 Testing alert...');
    alert('Alert test - JavaScript is working!');
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug Page</h1>
        
        {/* Auth Status */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Auth Status</h2>
          <div className="space-y-2">
            <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>
            <p><strong>Error:</strong> {error || 'None'}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Tests</h2>
          <div className="space-x-4">
            <Button onClick={testAlert}>Test Alert</Button>
            <Button onClick={testLogin} disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Test Login'}
            </Button>
            <Button onClick={() => addLog('🔄 Manual log entry')}>Add Log</Button>
            <Button onClick={() => setLogs([])}>Clear Logs</Button>
          </div>
        </div>

        {/* Logs */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Logs</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p>No logs yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
