import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { Award, Users, Globe, Heart, ArrowRight } from 'lucide-react'

export const metadata: Metadata = {
  title: 'About Us - Luxury Watches Store',
  description: 'Learn about our passion for luxury timepieces and commitment to exceptional service.',
}

const stats = [
  { label: 'Years of Experience', value: '25+', icon: Award },
  { label: 'Satisfied Customers', value: '10,000+', icon: Users },
  { label: 'Countries Served', value: '50+', icon: Globe },
  { label: 'Brands Partnered', value: '20+', icon: Heart },
]

const team = [
  {
    name: '<PERSON> Timekeeper',
    role: 'Founder & CEO',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
    bio: 'With over 30 years in luxury watches, <PERSON> founded our company with a vision to make exceptional timepieces accessible to discerning collectors worldwide.'
  },
  {
    name: '<PERSON> Horologist',
    role: 'Head of Curation',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
    bio: '<PERSON> brings 20 years of expertise in watch authentication and curation, ensuring every piece in our collection meets the highest standards.'
  },
  {
    name: 'David Chronos',
    role: 'Master Watchmaker',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
    bio: 'A certified master watchmaker with training from Switzerland, David oversees our restoration and maintenance services.'
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=1200&h=800&fit=crop&crop=center"
            alt="Luxury watch craftsmanship"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/60" />
        </div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold mb-6">
              Our <span className="text-luxury-gold">Story</span>
            </h1>
            <p className="text-xl leading-relaxed">
              For over two decades, we've been dedicated to bringing the world's finest luxury timepieces to passionate collectors and enthusiasts
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
            <div>
              <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-6">
                Our Mission
              </h2>
              <p className="text-gray-700 leading-relaxed mb-6">
                We believe that a luxury watch is more than just a timepiece—it's a work of art, a piece of history, and a personal statement. Our mission is to connect discerning collectors with exceptional timepieces that will be treasured for generations.
              </p>
              <p className="text-gray-700 leading-relaxed mb-8">
                Every watch in our collection is carefully selected for its craftsmanship, heritage, and investment potential. We work directly with authorized dealers and trusted sources to ensure authenticity and quality.
              </p>
              <Link
                href="/catalog"
                className="inline-flex items-center gap-2 px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
              >
                Explore Our Collection
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
            <div className="relative">
              <Image
                src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=600&fit=crop&crop=center"
                alt="Luxury watch collection"
                width={600}
                height={600}
                className="rounded-xl shadow-luxury"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-luxury-cream">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
              Our Achievements
            </h2>
            <p className="text-xl text-gray-600">
              Numbers that reflect our commitment to excellence
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div
                  key={index}
                  className="text-center bg-white p-8 rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300"
                >
                  <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-8 h-8 text-luxury-black" />
                  </div>
                  <div className="font-luxury-serif text-3xl font-bold text-luxury-black mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600">
                    {stat.label}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600">
              Passionate experts dedicated to exceptional service
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {team.map((member, index) => (
              <div
                key={index}
                className="text-center bg-white p-8 rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300"
              >
                <div className="relative w-32 h-32 mx-auto mb-6">
                  <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover rounded-full"
                  />
                </div>
                <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-2">
                  {member.name}
                </h3>
                <div className="text-luxury-gold font-semibold mb-4">
                  {member.role}
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {member.bio}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-luxury-cream">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-8">
              Our Values
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-luxury-black" />
                </div>
                <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-3">
                  Authenticity
                </h3>
                <p className="text-gray-600">
                  Every timepiece is guaranteed authentic with proper documentation and certification
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-luxury-black" />
                </div>
                <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-3">
                  Passion
                </h3>
                <p className="text-gray-600">
                  Our love for horology drives us to seek out the most exceptional timepieces
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-luxury-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-luxury-black" />
                </div>
                <h3 className="font-luxury-serif text-xl font-bold text-luxury-black mb-3">
                  Service
                </h3>
                <p className="text-gray-600">
                  Personalized service and expert guidance for every customer's unique needs
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-6">
            Ready to Find Your Perfect Timepiece?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Let our experts help you discover the luxury watch that perfectly matches your style and aspirations
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/catalog"
              className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
            >
              Browse Collection
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 px-8 py-4 border-2 border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
