@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Luxury Watch Store - Custom CSS Variables and Styles */
:root {
  /* Luxury Color Palette */
  --luxury-gold: #D4AF37;
  --luxury-gold-light: #F4E4BC;
  --luxury-gold-dark: #B8941F;
  --luxury-black: #0A0A0A;
  --luxury-charcoal: #1A1A1A;
  --luxury-silver: #C0C0C0;
  --luxury-platinum: #E5E4E2;
  --luxury-white: #FEFEFE;
  --luxury-cream: #F8F6F0;

  /* Typography */
  --font-luxury-serif: 'Playfair Display', serif;
  --font-luxury-sans: 'Inter', sans-serif;

  /* Shadows */
  --luxury-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  --luxury-shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);

  /* Transitions */
  --luxury-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--luxury-cream);
}

::-webkit-scrollbar-thumb {
  background: var(--luxury-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--luxury-gold-dark);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Base styles */
body {
  font-family: var(--font-luxury-sans);
  background-color: var(--luxury-white);
  color: var(--luxury-black);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility classes for luxury colors */
.bg-luxury-gold { background-color: var(--luxury-gold); }
.bg-luxury-gold-light { background-color: var(--luxury-gold-light); }
.bg-luxury-gold-dark { background-color: var(--luxury-gold-dark); }
.bg-luxury-black { background-color: var(--luxury-black); }
.bg-luxury-charcoal { background-color: var(--luxury-charcoal); }
.bg-luxury-silver { background-color: var(--luxury-silver); }
.bg-luxury-platinum { background-color: var(--luxury-platinum); }
.bg-luxury-white { background-color: var(--luxury-white); }
.bg-luxury-cream { background-color: var(--luxury-cream); }

.text-luxury-gold { color: var(--luxury-gold); }
.text-luxury-gold-light { color: var(--luxury-gold-light); }
.text-luxury-gold-dark { color: var(--luxury-gold-dark); }
.text-luxury-black { color: var(--luxury-black); }
.text-luxury-charcoal { color: var(--luxury-charcoal); }
.text-luxury-silver { color: var(--luxury-silver); }
.text-luxury-platinum { color: var(--luxury-platinum); }
.text-luxury-white { color: var(--luxury-white); }
.text-luxury-cream { color: var(--luxury-cream); }

.border-luxury-gold { border-color: var(--luxury-gold); }
.border-luxury-gold-dark { border-color: var(--luxury-gold-dark); }

/* Typography classes */
.font-luxury-serif { font-family: var(--font-luxury-serif); }
.font-luxury-sans { font-family: var(--font-luxury-sans); }

.heading-luxury {
  font-family: var(--font-luxury-serif);
  color: var(--luxury-black);
  font-weight: bold;
  letter-spacing: -0.025em;
}

.text-luxury-muted {
  color: #6b7280;
}

/* Shadow utilities */
.shadow-luxury { box-shadow: var(--luxury-shadow); }
.shadow-luxury-hover { box-shadow: var(--luxury-shadow-hover); }

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

/* Loading spinner */
.spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--luxury-gold);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Focus styles for accessibility */
.focus-luxury:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--luxury-gold);
}

/* Mega Menu Styles */
.mega-menu-backdrop {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.1);
}

.mega-menu-panel {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 246, 240, 0.98) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 175, 55, 0.1);
}

.mega-menu-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mega-menu-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(212, 175, 55, 0.1);
}

/* Full-width mega menu container */
.mega-menu-fullwidth {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

/* Grid system for mega menu */
.grid-cols-16 {
  grid-template-columns: repeat(16, minmax(0, 1fr));
}

/* Enhanced shadows for luxury feel */
.shadow-luxury-hover {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1), 0 4px 15px rgba(212, 175, 55, 0.15);
}

.shadow-luxury-deep {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(212, 175, 55, 0.2);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Luxury gradient backgrounds */
.bg-luxury-gradient {
  background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-gold-dark) 100%);
}

.bg-luxury-gradient-subtle {
  background: linear-gradient(135deg, var(--luxury-cream) 0%, var(--luxury-white) 100%);
}
