'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Edit, 
  Eye, 
  Calendar,
  User,
  Clock,
  Tag
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface ContentItem {
  id: string
  title: string
  type: 'blog-post' | 'page' | 'brand-page'
  status: 'published' | 'draft' | 'scheduled'
  author: string
  publishedAt?: string
  updatedAt: string
  excerpt?: string
  content: string
  tags: string[]
  seo: {
    title: string
    description: string
    keywords: string[]
  }
}

interface ContentPreviewProps {
  params: Promise<{ id: string }>
}

export default function ContentPreviewPage({ params }: ContentPreviewProps) {
  const router = useRouter()
  const [content, setContent] = useState<ContentItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [id, setId] = useState<string>('')

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setId(resolvedParams.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    if (!id) return

    const loadContent = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data based on ID
      const mockContent: Record<string, ContentItem> = {
        '1': {
          id: '1',
          title: 'Top 5 Luxury Watch Trends for 2025',
          type: 'blog-post',
          status: 'published',
          author: 'Marcus Timekeeper',
          publishedAt: '2025-06-15T10:00:00Z',
          updatedAt: '2025-06-15T10:00:00Z',
          excerpt: 'Discover the most exciting trends shaping the luxury watch industry in 2025...',
          content: `
            <h2>The Evolution of Luxury Timepieces</h2>
            <p>The luxury watch industry continues to evolve, blending traditional craftsmanship with modern innovation. Here are the top 5 trends defining 2025:</p>
            
            <h3>1. Sustainable Luxury</h3>
            <p>Environmental consciousness is driving luxury brands to adopt sustainable practices. From recycled materials to carbon-neutral manufacturing, sustainability is becoming a key differentiator.</p>
            
            <h3>2. Smart Integration</h3>
            <p>Traditional watchmakers are finding elegant ways to integrate smart features without compromising their classic aesthetic. Hybrid movements and discreet connectivity are leading this trend.</p>
            
            <h3>3. Vintage Revival</h3>
            <p>Classic designs from the 1960s and 1970s are making a strong comeback, with brands reissuing iconic models with modern improvements.</p>
            
            <h3>4. Personalization</h3>
            <p>Bespoke services and customization options are becoming more accessible, allowing collectors to create truly unique timepieces.</p>
            
            <h3>5. Investment Focus</h3>
            <p>Watches are increasingly viewed as alternative investments, with certain models appreciating significantly in value over time.</p>
          `,
          tags: ['trends', 'luxury', 'watches', '2025', 'investment'],
          seo: {
            title: 'Top 5 Luxury Watch Trends for 2025 | Luxury Watches Store',
            description: 'Discover the most exciting trends shaping the luxury watch industry in 2025, from sustainable luxury to smart integration.',
            keywords: ['luxury watches', 'watch trends 2025', 'sustainable luxury', 'watch investment']
          }
        },
        '2': {
          id: '2',
          title: 'How to Choose Your First Luxury Watch',
          type: 'blog-post',
          status: 'published',
          author: 'Elena Horologist',
          publishedAt: '2025-06-12T14:30:00Z',
          updatedAt: '2025-06-12T14:30:00Z',
          excerpt: 'A comprehensive guide for first-time luxury watch buyers...',
          content: `
            <h2>Your Journey into Luxury Timepieces</h2>
            <p>Choosing your first luxury watch is an exciting milestone. This comprehensive guide will help you make an informed decision that you'll cherish for years to come.</p>
            
            <h3>Understanding Your Style</h3>
            <p>Before diving into specific brands or models, it's important to understand your personal style and how a watch fits into your lifestyle.</p>
            
            <h3>Setting Your Budget</h3>
            <p>Luxury watches range from a few thousand to hundreds of thousands of dollars. Determine your budget early to focus your search effectively.</p>
            
            <h3>Key Considerations</h3>
            <ul>
              <li>Movement type (mechanical vs. quartz)</li>
              <li>Case size and material</li>
              <li>Water resistance</li>
              <li>Brand heritage and reputation</li>
              <li>Resale value</li>
            </ul>
          `,
          tags: ['guide', 'first-watch', 'luxury', 'buying-guide'],
          seo: {
            title: 'How to Choose Your First Luxury Watch - Complete Guide',
            description: 'A comprehensive guide for first-time luxury watch buyers covering style, budget, and key considerations.',
            keywords: ['first luxury watch', 'watch buying guide', 'luxury timepieces', 'watch selection']
          }
        }
      }
      
      setContent(mockContent[id] || null)
      setLoading(false)
    }

    loadContent()
  }, [id])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="h-10 w-20 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-64 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!content) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-12 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Content not found</h3>
            <p className="text-gray-600">The requested content could not be found.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-yellow-100 text-yellow-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'blog-post': return 'Blog Post'
      case 'page': return 'Page'
      case 'brand-page': return 'Brand Page'
      default: return type
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Content Preview</h1>
            <p className="text-gray-600">Preview how this content will appear</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/content/${content.id}/edit`}>
            <Button variant="outline" className="flex items-center gap-2">
              <Edit className="w-4 h-4" />
              Edit
            </Button>
          </Link>
          <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black flex items-center gap-2">
            <Eye className="w-4 h-4" />
            View Live
          </Button>
        </div>
      </div>

      {/* Content Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>Content Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <p className="font-medium">{getTypeLabel(content.type)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(content.status)}`}>
                {content.status.charAt(0).toUpperCase() + content.status.slice(1)}
              </span>
            </div>
            <div>
              <p className="text-sm text-gray-500">Author</p>
              <p className="font-medium">{content.author}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">
                {content.publishedAt ? 'Published' : 'Updated'}
              </p>
              <p className="font-medium">
                {new Date(content.publishedAt || content.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          {content.tags.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-500 mb-2">Tags</p>
              <div className="flex flex-wrap gap-2">
                {content.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Preview */}
      <Card>
        <CardHeader>
          <CardTitle>{content.title}</CardTitle>
          {content.excerpt && (
            <p className="text-gray-600">{content.excerpt}</p>
          )}
        </CardHeader>
        <CardContent>
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content.content }}
          />
        </CardContent>
      </Card>

      {/* SEO Information */}
      <Card>
        <CardHeader>
          <CardTitle>SEO Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium text-gray-700">SEO Title</p>
            <p className="text-sm text-gray-600">{content.seo.title}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-700">Meta Description</p>
            <p className="text-sm text-gray-600">{content.seo.description}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-700">Keywords</p>
            <div className="flex flex-wrap gap-2 mt-1">
              {content.seo.keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
