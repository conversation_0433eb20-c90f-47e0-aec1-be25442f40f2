'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  FileText,
  Calendar,
  User,
  MoreHorizontal
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface ContentItem {
  id: string
  title: string
  type: 'blog-post' | 'page' | 'brand-page'
  status: 'published' | 'draft' | 'scheduled'
  author: string
  publishedAt?: string
  updatedAt: string
  excerpt?: string
  views: number
}

export default function ContentPage() {
  const [content, setContent] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')

  useEffect(() => {
    const loadContent = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setContent([
        {
          id: '1',
          title: 'Top 5 Luxury Watch Trends for 2025',
          type: 'blog-post',
          status: 'published',
          author: 'Marcus Timekeeper',
          publishedAt: '2025-06-15T10:00:00Z',
          updatedAt: '2025-06-15T10:00:00Z',
          excerpt: 'Discover the most exciting trends shaping the luxury watch industry in 2025...',
          views: 1250
        },
        {
          id: '2',
          title: 'How to Choose Your First Luxury Watch',
          type: 'blog-post',
          status: 'published',
          author: 'Elena Horologist',
          publishedAt: '2025-06-12T14:30:00Z',
          updatedAt: '2025-06-12T14:30:00Z',
          excerpt: 'A comprehensive guide for first-time luxury watch buyers...',
          views: 890
        },
        {
          id: '3',
          title: 'The Investment Value of Vintage Timepieces',
          type: 'blog-post',
          status: 'draft',
          author: 'David Chronos',
          updatedAt: '2025-06-10T09:15:00Z',
          excerpt: 'Explore why vintage luxury watches have become one of the most sought-after...',
          views: 0
        },
        {
          id: '4',
          title: 'Rolex Brand Heritage',
          type: 'brand-page',
          status: 'published',
          author: 'Admin',
          publishedAt: '2025-06-01T00:00:00Z',
          updatedAt: '2025-06-01T00:00:00Z',
          views: 2340
        },
        {
          id: '5',
          title: 'About Our Company',
          type: 'page',
          status: 'published',
          author: 'Admin',
          publishedAt: '2025-05-15T00:00:00Z',
          updatedAt: '2025-06-10T00:00:00Z',
          views: 567
        }
      ])
      
      setLoading(false)
    }

    loadContent()
  }, [])

  const filteredContent = content.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.author.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || item.type === selectedType
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const contentTypes = ['all', 'blog-post', 'page', 'brand-page']
  const statuses = ['all', 'published', 'draft', 'scheduled']

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-yellow-100 text-yellow-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'blog-post': return FileText
      case 'page': return FileText
      case 'brand-page': return FileText
      default: return FileText
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'blog-post': return 'Blog Post'
      case 'page': return 'Page'
      case 'brand-page': return 'Brand Page'
      default: return type
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage blog posts, pages, and brand content</p>
        </div>
        <div className="flex gap-2">
          <Link href="/admin/content/new?type=blog-post">
            <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black">
              <Plus className="w-4 h-4 mr-2" />
              New Blog Post
            </Button>
          </Link>
          <Link href="/admin/content/new?type=page">
            <Button variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              New Page
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search content..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
              >
                {contentTypes.map(type => (
                  <option key={type} value={type}>
                    {type === 'all' ? 'All Types' : getTypeLabel(type)}
                  </option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
              >
                {statuses.map(status => (
                  <option key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content List */}
      <div className="space-y-4">
        {filteredContent.map((item) => {
          const TypeIcon = getTypeIcon(item.type)
          
          return (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <div className="p-2 bg-luxury-cream rounded-lg">
                      <TypeIcon className="w-5 h-5 text-luxury-gold" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">{item.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                        </span>
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
                          {getTypeLabel(item.type)}
                        </span>
                      </div>
                      
                      {item.excerpt && (
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {item.excerpt}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-6 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          {item.author}
                        </div>
                        {item.publishedAt && (
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {new Date(item.publishedAt).toLocaleDateString()}
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Eye className="w-4 h-4" />
                          {item.views.toLocaleString()} views
                        </div>
                        <div>
                          Updated: {new Date(item.updatedAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Link href={`/admin/content/${item.id}/preview`}>
                      <button className="p-2 hover:bg-gray-100 rounded-lg">
                        <Eye className="w-4 h-4 text-gray-600" />
                      </button>
                    </Link>
                    <Link href={`/admin/content/${item.id}/edit`}>
                      <button className="p-2 hover:bg-gray-100 rounded-lg">
                        <Edit className="w-4 h-4 text-gray-600" />
                      </button>
                    </Link>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Trash2 className="w-4 h-4 text-red-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <MoreHorizontal className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredContent.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedType !== 'all' || selectedStatus !== 'all'
                ? 'Try adjusting your filters to see more content.'
                : 'Get started by creating your first blog post or page.'}
            </p>
            <div className="flex justify-center gap-2">
              <Link href="/admin/content/new?type=blog-post">
                <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black">
                  <Plus className="w-4 h-4 mr-2" />
                  New Blog Post
                </Button>
              </Link>
              <Link href="/admin/content/new?type=page">
                <Button variant="outline">
                  <Plus className="w-4 h-4 mr-2" />
                  New Page
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
