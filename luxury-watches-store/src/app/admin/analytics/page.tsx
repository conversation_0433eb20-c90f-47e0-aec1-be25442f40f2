'use client'

import { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  TrendingDown,
  Eye, 
  Users, 
  Search,
  Target,
  Globe,
  Smartphone,
  Monitor,
  BarChart3,
  PieChart,
  Activity,
  Zap
} from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface AnalyticsData {
  overview: {
    pageViews: number
    uniqueVisitors: number
    bounceRate: number
    avgSessionDuration: string
    conversionRate: number
  }
  traffic: {
    organic: number
    direct: number
    social: number
    referral: number
    paid: number
  }
  devices: {
    desktop: number
    mobile: number
    tablet: number
  }
  topPages: Array<{
    page: string
    views: number
    change: number
  }>
  keywords: Array<{
    keyword: string
    position: number
    clicks: number
    impressions: number
  }>
  seoScore: {
    overall: number
    technical: number
    content: number
    performance: number
  }
}

export default function AnalyticsPage() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')

  useEffect(() => {
    const loadAnalytics = async () => {
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      setData({
        overview: {
          pageViews: 12450,
          uniqueVisitors: 8920,
          bounceRate: 32.5,
          avgSessionDuration: '3m 24s',
          conversionRate: 3.2
        },
        traffic: {
          organic: 45,
          direct: 28,
          social: 15,
          referral: 8,
          paid: 4
        },
        devices: {
          desktop: 52,
          mobile: 38,
          tablet: 10
        },
        topPages: [
          { page: '/catalog', views: 3240, change: 12.5 },
          { page: '/', views: 2890, change: 8.3 },
          { page: '/brands/rolex', views: 1560, change: -2.1 },
          { page: '/blog/luxury-watch-trends-2025', views: 1230, change: 25.7 },
          { page: '/catalog/rolex-submariner', views: 980, change: 15.2 }
        ],
        keywords: [
          { keyword: 'luxury watches', position: 3, clicks: 450, impressions: 12000 },
          { keyword: 'rolex submariner', position: 5, clicks: 320, impressions: 8500 },
          { keyword: 'patek philippe', position: 7, clicks: 280, impressions: 6200 },
          { keyword: 'luxury timepieces', position: 4, clicks: 190, impressions: 4800 },
          { keyword: 'swiss watches', position: 6, clicks: 150, impressions: 3900 }
        ],
        seoScore: {
          overall: 87,
          technical: 92,
          content: 85,
          performance: 84
        }
      })
      
      setLoading(false)
    }

    loadAnalytics()
  }, [timeRange])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!data) return null

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-100'
    if (score >= 70) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics & SEO</h1>
          <p className="text-gray-600">Monitor performance and search optimization</p>
        </div>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Page Views</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.overview.pageViews.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +12.5%
                </p>
              </div>
              <Eye className="w-8 h-8 text-luxury-gold" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Unique Visitors</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.overview.uniqueVisitors.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +8.3%
                </p>
              </div>
              <Users className="w-8 h-8 text-luxury-gold" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Bounce Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.overview.bounceRate}%
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingDown className="w-3 h-3" />
                  -2.1%
                </p>
              </div>
              <Activity className="w-8 h-8 text-luxury-gold" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Session</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.overview.avgSessionDuration}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +15.2%
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-luxury-gold" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.overview.conversionRate}%
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  +0.8%
                </p>
              </div>
              <Target className="w-8 h-8 text-luxury-gold" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Traffic Sources */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Traffic Sources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(data.traffic).map(([source, percentage]) => (
                <div key={source} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">{source}</span>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-luxury-gold h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-8">{percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              Device Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Monitor className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium">Desktop</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-luxury-gold h-2 rounded-full"
                      style={{ width: `${data.devices.desktop}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-8">{data.devices.desktop}%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Smartphone className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium">Mobile</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-luxury-gold h-2 rounded-full"
                      style={{ width: `${data.devices.mobile}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-8">{data.devices.mobile}%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <PieChart className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium">Tablet</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-luxury-gold h-2 rounded-full"
                      style={{ width: `${data.devices.tablet}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-8">{data.devices.tablet}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SEO Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            SEO Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full ${getScoreBackground(data.seoScore.overall)} flex items-center justify-center mx-auto mb-2`}>
                <span className={`text-xl font-bold ${getScoreColor(data.seoScore.overall)}`}>
                  {data.seoScore.overall}
                </span>
              </div>
              <p className="text-sm font-medium">Overall Score</p>
            </div>
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full ${getScoreBackground(data.seoScore.technical)} flex items-center justify-center mx-auto mb-2`}>
                <span className={`text-xl font-bold ${getScoreColor(data.seoScore.technical)}`}>
                  {data.seoScore.technical}
                </span>
              </div>
              <p className="text-sm font-medium">Technical SEO</p>
            </div>
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full ${getScoreBackground(data.seoScore.content)} flex items-center justify-center mx-auto mb-2`}>
                <span className={`text-xl font-bold ${getScoreColor(data.seoScore.content)}`}>
                  {data.seoScore.content}
                </span>
              </div>
              <p className="text-sm font-medium">Content Quality</p>
            </div>
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full ${getScoreBackground(data.seoScore.performance)} flex items-center justify-center mx-auto mb-2`}>
                <span className={`text-xl font-bold ${getScoreColor(data.seoScore.performance)}`}>
                  {data.seoScore.performance}
                </span>
              </div>
              <p className="text-sm font-medium">Performance</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Pages and Keywords */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Pages */}
        <Card>
          <CardHeader>
            <CardTitle>Top Pages</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.topPages.map((page, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{page.page}</p>
                    <p className="text-xs text-gray-500">{page.views.toLocaleString()} views</p>
                  </div>
                  <div className={`text-sm font-medium ${
                    page.change > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {page.change > 0 ? '+' : ''}{page.change}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Keywords */}
        <Card>
          <CardHeader>
            <CardTitle>Top Keywords</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.keywords.map((keyword, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{keyword.keyword}</p>
                    <p className="text-xs text-gray-500">
                      {keyword.clicks} clicks • {keyword.impressions.toLocaleString()} impressions
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-medium ${
                      keyword.position <= 3 ? 'text-green-600' :
                      keyword.position <= 10 ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      #{keyword.position}
                    </div>
                    <p className="text-xs text-gray-500">Position</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
