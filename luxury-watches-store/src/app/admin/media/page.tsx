'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { 
  Upload, 
  Search, 
  Filter, 
  Grid3X3, 
  List,
  Download,
  Trash2,
  Edit,
  Copy,
  Eye,
  Zap,
  Image as ImageIcon,
  FileImage,
  Folder
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface MediaItem {
  id: string
  name: string
  url: string
  type: 'image' | 'video' | 'document'
  size: number
  dimensions?: { width: number; height: number }
  uploadedAt: string
  usedIn: string[]
  alt?: string
  tags: string[]
}

export default function MediaPage() {
  const [media, setMedia] = useState<MediaItem[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)

  useEffect(() => {
    const loadMedia = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setMedia([
        {
          id: '1',
          name: 'rolex-submariner-hero.jpg',
          url: 'https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?w=800&h=600',
          type: 'image',
          size: 245000,
          dimensions: { width: 1920, height: 1080 },
          uploadedAt: '2025-06-15T10:00:00Z',
          usedIn: ['Product: Rolex Submariner', 'Blog: Watch Trends 2025'],
          alt: 'Rolex Submariner luxury watch',
          tags: ['rolex', 'submariner', 'luxury', 'watch']
        },
        {
          id: '2',
          name: 'patek-philippe-nautilus.jpg',
          url: 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=800&h=600',
          type: 'image',
          size: 189000,
          dimensions: { width: 1600, height: 1200 },
          uploadedAt: '2025-06-14T15:30:00Z',
          usedIn: ['Product: Patek Philippe Nautilus'],
          alt: 'Patek Philippe Nautilus luxury timepiece',
          tags: ['patek-philippe', 'nautilus', 'luxury', 'timepiece']
        },
        {
          id: '3',
          name: 'luxury-watch-collection.jpg',
          url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600',
          type: 'image',
          size: 312000,
          dimensions: { width: 2048, height: 1365 },
          uploadedAt: '2025-06-13T09:15:00Z',
          usedIn: ['Homepage Hero', 'About Page'],
          alt: 'Luxury watch collection display',
          tags: ['collection', 'luxury', 'display', 'multiple']
        },
        {
          id: '4',
          name: 'omega-speedmaster.jpg',
          url: 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=800&h=600',
          type: 'image',
          size: 198000,
          dimensions: { width: 1800, height: 1200 },
          uploadedAt: '2025-06-12T14:20:00Z',
          usedIn: ['Product: Omega Speedmaster'],
          alt: 'Omega Speedmaster professional watch',
          tags: ['omega', 'speedmaster', 'professional', 'space']
        }
      ])
      
      setLoading(false)
    }

    loadMedia()
  }, [])

  const filteredMedia = media.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesType = selectedType === 'all' || item.type === selectedType
    
    return matchesSearch && matchesType
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    setUploadProgress(0)
    
    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 100))
      setUploadProgress(i)
    }
    
    // Simulate adding new files to media library
    const newMedia = files.map((file, index) => ({
      id: `new-${Date.now()}-${index}`,
      name: file.name,
      url: URL.createObjectURL(file),
      type: file.type.startsWith('image/') ? 'image' as const : 'document' as const,
      size: file.size,
      dimensions: file.type.startsWith('image/') ? { width: 1920, height: 1080 } : undefined,
      uploadedAt: new Date().toISOString(),
      usedIn: [],
      alt: '',
      tags: []
    }))
    
    setMedia(prev => [...newMedia, ...prev])
    setUploadProgress(null)
  }

  const toggleSelection = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const copyImageUrl = (url: string) => {
    navigator.clipboard.writeText(url)
    // You could show a toast notification here
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Media Library</h1>
          <p className="text-gray-600">Manage images, videos, and documents</p>
        </div>
        <div className="flex gap-2">
          <input
            type="file"
            multiple
            accept="image/*,video/*,.pdf,.doc,.docx"
            onChange={handleFileUpload}
            className="hidden"
            id="media-upload"
          />
          <label htmlFor="media-upload">
            <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black cursor-pointer">
              <Upload className="w-4 h-4 mr-2" />
              Upload Media
            </Button>
          </label>
        </div>
      </div>

      {/* Upload Progress */}
      {uploadProgress !== null && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Upload className="w-5 h-5 text-luxury-gold" />
              <div className="flex-1">
                <div className="flex justify-between text-sm mb-1">
                  <span>Uploading files...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-luxury-gold h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and View Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search media..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-4 items-center">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="document">Documents</option>
              </select>
              <div className="flex border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-luxury-gold text-luxury-black' : 'text-gray-600 hover:bg-gray-100'}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-luxury-gold text-luxury-black' : 'text-gray-600 hover:bg-gray-100'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {selectedItems.length} item(s) selected
              </span>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
                <Button variant="outline" size="sm">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Media Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {filteredMedia.map((item) => (
            <div
              key={item.id}
              className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                selectedItems.includes(item.id) 
                  ? 'border-luxury-gold shadow-lg' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => toggleSelection(item.id)}
            >
              <div className="aspect-square relative">
                {item.type === 'image' ? (
                  <Image
                    src={item.url}
                    alt={item.alt || item.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <FileImage className="w-8 h-8 text-gray-400" />
                  </div>
                )}
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="flex gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        copyImageUrl(item.url)
                      }}
                      className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                    >
                      <Copy className="w-4 h-4 text-white" />
                    </button>
                    <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                      <Eye className="w-4 h-4 text-white" />
                    </button>
                    <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                      <Edit className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="p-2">
                <p className="text-xs font-medium text-gray-900 truncate">
                  {item.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(item.size)}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {filteredMedia.map((item) => (
            <Card
              key={item.id}
              className={`cursor-pointer transition-all ${
                selectedItems.includes(item.id) 
                  ? 'border-luxury-gold shadow-lg' 
                  : 'hover:shadow-md'
              }`}
              onClick={() => toggleSelection(item.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 relative rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                    {item.type === 'image' ? (
                      <Image
                        src={item.url}
                        alt={item.alt || item.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <FileImage className="w-6 h-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{item.name}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                      <span>{formatFileSize(item.size)}</span>
                      {item.dimensions && (
                        <span>{item.dimensions.width} × {item.dimensions.height}</span>
                      )}
                      <span>{new Date(item.uploadedAt).toLocaleDateString()}</span>
                    </div>
                    {item.usedIn.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">
                          Used in: {item.usedIn.join(', ')}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        copyImageUrl(item.url)
                      }}
                      className="p-2 hover:bg-gray-100 rounded-lg"
                    >
                      <Copy className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Eye className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Edit className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Trash2 className="w-4 h-4 text-red-600" />
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {filteredMedia.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No media found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedType !== 'all'
                ? 'Try adjusting your filters to see more media.'
                : 'Upload your first images, videos, or documents.'}
            </p>
            <label htmlFor="media-upload">
              <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black cursor-pointer">
                <Upload className="w-4 h-4 mr-2" />
                Upload Media
              </Button>
            </label>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
