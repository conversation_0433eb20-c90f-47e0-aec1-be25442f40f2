import {
  TrendingUp,
  Package,
  Users,
  ShoppingCart,
  Eye,
  DollarSign,
  AlertTriangle,
  Clock
} from 'lucide-react'

interface DashboardStats {
  totalProducts: number
  totalOrders: number
  totalCustomers: number
  totalRevenue: number
  lowStockItems: number
  pendingOrders: number
  pageViews: number
  conversionRate: number
}

interface RecentActivity {
  id: string
  type: 'order' | 'product' | 'customer'
  message: string
  timestamp: string
}

export default function AdminDashboard() {
  // Simplified static data for testing
  const stats = {
    totalProducts: 24,
    totalOrders: 156,
    totalCustomers: 89,
    totalRevenue: 245000,
    lowStockItems: 3,
    pendingOrders: 8,
    pageViews: 12450,
    conversionRate: 3.2
  }

  const recentActivity = [
    {
      id: '1',
      type: 'order',
      message: 'New order #1234 for Rolex Submariner',
      timestamp: '2 minutes ago'
    },
    {
      id: '2',
      type: 'product',
      message: 'Product "Patek Philippe Nautilus" updated',
      timestamp: '15 minutes ago'
    }
  ]



  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-luxury-gold to-luxury-gold-dark rounded-lg p-6 text-luxury-black">
        <h1 className="text-2xl font-bold mb-2">Welcome to Luxury Watches Admin</h1>
        <p className="opacity-90">
          Manage your luxury timepiece collection with precision and elegance
        </p>
      </div>

      {/* Simple Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue.toLocaleString()}</p>
              <p className="text-sm text-green-600">+12.5%</p>
            </div>
            <DollarSign className="w-8 h-8 text-luxury-gold" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
              <p className="text-sm text-green-600">+8.2%</p>
            </div>
            <ShoppingCart className="w-8 h-8 text-luxury-gold" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
              <p className="text-sm text-gray-600">+2 this week</p>
            </div>
            <Package className="w-8 h-8 text-luxury-gold" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
              <p className="text-sm text-green-600">+15.3%</p>
            </div>
            <Users className="w-8 h-8 text-luxury-gold" />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50">
              <div className="w-2 h-2 rounded-full mt-2 bg-green-500" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {activity.message}
                </p>
                <p className="text-xs text-gray-500">
                  {activity.timestamp}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
