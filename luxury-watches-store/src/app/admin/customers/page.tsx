'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  Search, 
  Filter, 
  Eye, 
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  DollarSign,
  User,
  MoreHorizontal,
  UserPlus
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface Customer {
  id: string
  name: string
  email: string
  phone?: string
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  registeredAt: string
  lastOrderAt?: string
  totalOrders: number
  totalSpent: number
  status: 'active' | 'inactive' | 'blocked'
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedTier, setSelectedTier] = useState('all')

  useEffect(() => {
    const loadCustomers = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setCustomers([
        {
          id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '+****************',
          address: {
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA'
          },
          registeredAt: '2024-01-15T10:30:00Z',
          lastOrderAt: '2025-06-15T14:20:00Z',
          totalOrders: 3,
          totalSpent: 45000,
          status: 'active',
          tier: 'gold'
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          address: {
            street: '456 Oak Ave',
            city: 'Los Angeles',
            state: 'CA',
            zipCode: '90210',
            country: 'USA'
          },
          registeredAt: '2024-03-20T09:15:00Z',
          lastOrderAt: '2025-06-14T08:45:00Z',
          totalOrders: 5,
          totalSpent: 125000,
          status: 'active',
          tier: 'platinum'
        },
        {
          id: '3',
          name: 'Michael Brown',
          email: '<EMAIL>',
          address: {
            street: '789 Pine St',
            city: 'Chicago',
            state: 'IL',
            zipCode: '60601',
            country: 'USA'
          },
          registeredAt: '2025-06-10T16:20:00Z',
          totalOrders: 0,
          totalSpent: 0,
          status: 'active',
          tier: 'bronze'
        },
        {
          id: '4',
          name: 'Emily Davis',
          email: '<EMAIL>',
          phone: '+****************',
          address: {
            street: '321 Elm St',
            city: 'Miami',
            state: 'FL',
            zipCode: '33101',
            country: 'USA'
          },
          registeredAt: '2024-08-12T11:00:00Z',
          lastOrderAt: '2025-06-12T15:30:00Z',
          totalOrders: 2,
          totalSpent: 18500,
          status: 'active',
          tier: 'silver'
        },
        {
          id: '5',
          name: 'Robert Wilson',
          email: '<EMAIL>',
          address: {
            street: '654 Maple Dr',
            city: 'Seattle',
            state: 'WA',
            zipCode: '98101',
            country: 'USA'
          },
          registeredAt: '2024-02-28T14:45:00Z',
          lastOrderAt: '2024-12-15T10:20:00Z',
          totalOrders: 1,
          totalSpent: 6500,
          status: 'inactive',
          tier: 'bronze'
        }
      ])
      
      setLoading(false)
    }

    loadCustomers()
  }, [])

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || customer.status === selectedStatus
    const matchesTier = selectedTier === 'all' || customer.tier === selectedTier
    
    return matchesSearch && matchesStatus && matchesTier
  })

  const statuses = ['all', 'active', 'inactive', 'blocked']
  const tiers = ['all', 'bronze', 'silver', 'gold', 'platinum']

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-yellow-100 text-yellow-800'
      case 'blocked': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'bg-orange-100 text-orange-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'platinum': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTierIcon = (tier: string) => {
    // You could return different icons for different tiers
    return User
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600">Manage customer accounts and relationships</p>
        </div>
        <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black">
          <UserPlus className="w-4 h-4 mr-2" />
          Add Customer
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
              </div>
              <User className="w-8 h-8 text-luxury-gold" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Customers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {customers.filter(c => c.status === 'active').length}
                </p>
              </div>
              <User className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${customers.reduce((sum, customer) => sum + customer.totalSpent, 0).toLocaleString()}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Order Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${Math.round(customers.reduce((sum, customer) => sum + customer.totalSpent, 0) / 
                    customers.reduce((sum, customer) => sum + customer.totalOrders, 0) || 0).toLocaleString()}
                </p>
              </div>
              <ShoppingBag className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search customers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
              >
                {statuses.map(status => (
                  <option key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>
              <select
                value={selectedTier}
                onChange={(e) => setSelectedTier(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
              >
                {tiers.map(tier => (
                  <option key={tier} value={tier}>
                    {tier === 'all' ? 'All Tiers' : tier.charAt(0).toUpperCase() + tier.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customers List */}
      <div className="space-y-4">
        {filteredCustomers.map((customer) => {
          const TierIcon = getTierIcon(customer.tier)
          
          return (
            <Card key={customer.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <div className="p-2 bg-luxury-cream rounded-lg">
                      <TierIcon className="w-5 h-5 text-luxury-gold" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">{customer.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                          {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTierColor(customer.tier)}`}>
                          {customer.tier.charAt(0).toUpperCase() + customer.tier.slice(1)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-500">Contact</p>
                          <p className="text-sm font-medium">{customer.email}</p>
                          {customer.phone && (
                            <p className="text-sm text-gray-600">{customer.phone}</p>
                          )}
                        </div>
                        
                        <div>
                          <p className="text-sm text-gray-500">Location</p>
                          <p className="text-sm">{customer.address.city}, {customer.address.state}</p>
                          <p className="text-sm text-gray-600">{customer.address.country}</p>
                        </div>
                        
                        <div>
                          <p className="text-sm text-gray-500">Orders & Spending</p>
                          <p className="text-sm font-medium">{customer.totalOrders} orders</p>
                          <p className="text-sm font-bold text-green-600">${customer.totalSpent.toLocaleString()}</p>
                        </div>
                        
                        <div>
                          <p className="text-sm text-gray-500">Registration</p>
                          <p className="text-sm">{new Date(customer.registeredAt).toLocaleDateString()}</p>
                          {customer.lastOrderAt && (
                            <p className="text-sm text-gray-600">
                              Last order: {new Date(customer.lastOrderAt).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Mail className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Eye className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Edit className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <MoreHorizontal className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredCustomers.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedStatus !== 'all' || selectedTier !== 'all'
                ? 'Try adjusting your filters to see more customers.'
                : 'Customers will appear here when they register on your site.'}
            </p>
            <Button className="bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-black">
              <UserPlus className="w-4 h-4 mr-2" />
              Add Customer
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
