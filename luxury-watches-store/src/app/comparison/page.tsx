'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { X, ShoppingBag, Heart, ArrowLeft } from 'lucide-react';
import { useComparison } from '@/contexts/comparison-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useToastActions } from '@/components/ui/toast';
import { Button } from '@/components/ui/button';
import { formatPrice, cn } from '@/lib/utils';

const COMPARISON_FEATURES = [
  { key: 'brand', label: 'Brand' },
  { key: 'price', label: 'Price' },
  { key: 'movement', label: 'Movement' },
  { key: 'caseMaterial', label: 'Case Material' },
  { key: 'caseSize', label: 'Case Size' },
  { key: 'waterResistance', label: 'Water Resistance' },
  { key: 'features', label: 'Features' },
  { key: 'warranty', label: 'Warranty' },
];

export default function ComparisonPage() {
  const { items, removeItem, clearComparison } = useComparison();
  const { addItem: addToCart } = useCart();
  const { addItem: addToWishlist, isInWishlist } = useWishlist();
  const { showSuccess } = useToastActions();
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  const handleImageError = (watchId: string) => {
    setImageErrors(prev => ({ ...prev, [watchId]: true }));
  };

  const handleAddToCart = (watch: any) => {
    addToCart(watch, 1);
    showSuccess('Added to cart', `${watch.name} has been added to your cart`);
  };

  const handleAddToWishlist = (watch: any) => {
    if (!isInWishlist(watch.id)) {
      addToWishlist(watch);
      showSuccess('Added to wishlist', `${watch.name} has been added to your wishlist`);
    }
  };

  const handleRemoveItem = (watchId: string, watchName: string) => {
    removeItem(watchId);
    showSuccess('Removed from comparison', `${watchName} has been removed from comparison`);
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-luxury-cream py-12">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <div className="w-24 h-24 mx-auto mb-6 bg-luxury-gold/10 rounded-full flex items-center justify-center">
              <div className="text-luxury-gold text-4xl">⚖️</div>
            </div>
            <h1 className="font-luxury-serif text-3xl font-bold text-luxury-black mb-4">
              No Watches to Compare
            </h1>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Add watches to your comparison list to see detailed side-by-side comparisons of their features and specifications.
            </p>
            <Link href="/catalog">
              <Button variant="luxury" size="lg">
                Browse Watches
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-luxury-cream py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Link href="/catalog">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Catalog
                </Button>
              </Link>
              <div>
                <h1 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black">
                  Watch Comparison
                </h1>
                <p className="text-gray-600">
                  Compare {items.length} watch{items.length !== 1 ? 'es' : ''} side by side
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={clearComparison}
              className="text-red-600 border-red-600 hover:bg-red-50"
            >
              Clear All
            </Button>
          </div>
        </div>

        {/* Comparison Table */}
        <div className="bg-white rounded-xl shadow-luxury overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              {/* Watch Images and Basic Info */}
              <thead>
                <tr className="border-b border-gray-200">
                  <td className="p-6 font-medium text-gray-900 bg-gray-50 sticky left-0 z-10">
                    Watch
                  </td>
                  {items.map((watch) => (
                    <td key={watch.id} className="p-6 text-center min-w-80">
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveItem(watch.id, watch.name)}
                          className="absolute -top-2 -right-2 z-10 bg-white shadow-md hover:bg-red-50 text-red-600"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        
                        <div className="relative h-48 bg-luxury-cream rounded-lg mb-4 overflow-hidden">
                          {!imageErrors[watch.id] ? (
                            <Image
                              src={watch.images[0]}
                              alt={`${watch.brand} ${watch.name}`}
                              fill
                              className="object-cover"
                              onError={() => handleImageError(watch.id)}
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full text-luxury-gold text-4xl">
                              ⌚
                            </div>
                          )}
                        </div>
                        
                        <h3 className="font-luxury-serif text-lg font-semibold text-luxury-black mb-1">
                          {watch.name}
                        </h3>
                        <p className="text-luxury-gold text-sm font-medium mb-2">
                          {watch.brand}
                        </p>
                        <p className="text-luxury-gold font-bold text-xl mb-4">
                          {formatPrice(watch.price)}
                        </p>
                        
                        <div className="flex flex-col gap-2">
                          <Button
                            onClick={() => handleAddToCart(watch)}
                            disabled={!watch.inStock}
                            variant={watch.inStock ? "luxury" : "secondary"}
                            size="sm"
                            className="w-full"
                          >
                            <ShoppingBag className="h-4 w-4 mr-2" />
                            {watch.inStock ? 'Add to Cart' : 'Out of Stock'}
                          </Button>
                          
                          <Button
                            onClick={() => handleAddToWishlist(watch)}
                            variant="outline"
                            size="sm"
                            className="w-full"
                            disabled={isInWishlist(watch.id)}
                          >
                            <Heart className={cn(
                              "h-4 w-4 mr-2",
                              isInWishlist(watch.id) && "fill-current text-red-500"
                            )} />
                            {isInWishlist(watch.id) ? 'In Wishlist' : 'Add to Wishlist'}
                          </Button>
                          
                          <Link href={`/catalog/${watch.id}`}>
                            <Button variant="ghost" size="sm" className="w-full">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </td>
                  ))}
                </tr>
              </thead>

              {/* Comparison Features */}
              <tbody>
                {COMPARISON_FEATURES.map((feature, index) => (
                  <motion.tr
                    key={feature.key}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={cn(
                      "border-b border-gray-100",
                      index % 2 === 0 ? "bg-gray-50/50" : "bg-white"
                    )}
                  >
                    <td className="p-4 font-medium text-gray-900 bg-gray-50 sticky left-0 z-10">
                      {feature.label}
                    </td>
                    {items.map((watch) => (
                      <td key={watch.id} className="p-4 text-center">
                        {feature.key === 'price' ? (
                          <span className="font-semibold text-luxury-gold">
                            {formatPrice(watch.price)}
                          </span>
                        ) : feature.key === 'features' ? (
                          <div className="flex flex-wrap gap-1 justify-center">
                            {watch.features.slice(0, 3).map((feat: string, idx: number) => (
                              <span
                                key={idx}
                                className="bg-luxury-cream text-luxury-black px-2 py-1 rounded text-xs"
                              >
                                {feat}
                              </span>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-700">
                            {(watch as any)[feature.key] || 'N/A'}
                          </span>
                        )}
                      </td>
                    ))}
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add More Watches */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 mb-4">
            Want to compare more watches? You can add up to 4 watches for comparison.
          </p>
          <Link href="/catalog">
            <Button variant="outline">
              Add More Watches
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
