'use client';

import { createContext, useContext, useReducer, useEffect } from 'react';
import { WishlistItem, SimpleWatch } from '@/lib/types';
import { generateId } from '@/lib/utils';

interface WishlistState {
  items: WishlistItem[];
  itemCount: number;
}

type WishlistAction =
  | { type: 'ADD_ITEM'; payload: SimpleWatch }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_WISHLIST' }
  | { type: 'LOAD_WISHLIST'; payload: WishlistItem[] };

interface WishlistContextType extends WishlistState {
  addItem: (watch: SimpleWatch) => void;
  removeItem: (watchId: string) => void;
  isInWishlist: (watchId: string) => boolean;
  clearWishlist: () => void;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

const WISHLIST_STORAGE_KEY = 'luxury-watches-wishlist';

const initialState: WishlistState = {
  items: [],
  itemCount: 0,
};

function wishlistReducer(state: WishlistState, action: WishlistAction): WishlistState {
  switch (action.type) {
    case 'ADD_ITEM': {
      const watch = action.payload;
      const existingItem = state.items.find(item => item.watchId === watch.id);

      if (existingItem) {
        // Item already in wishlist, don't add again
        return state;
      }

      const newItem: WishlistItem = {
        id: generateId(),
        watchId: watch.id,
        watch,
        addedAt: new Date().toISOString(),
      };

      const newItems = [...state.items, newItem];

      return {
        ...state,
        items: newItems,
        itemCount: newItems.length,
      };
    }

    case 'REMOVE_ITEM': {
      const watchId = action.payload;
      const newItems = state.items.filter(item => item.watchId !== watchId);

      return {
        ...state,
        items: newItems,
        itemCount: newItems.length,
      };
    }

    case 'CLEAR_WISHLIST':
      return {
        ...state,
        items: [],
        itemCount: 0,
      };

    case 'LOAD_WISHLIST': {
      const items = action.payload;
      return {
        ...state,
        items,
        itemCount: items.length,
      };
    }

    default:
      return state;
  }
}

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(wishlistReducer, initialState);

  // Load wishlist from localStorage on mount
  useEffect(() => {
    try {
      const savedWishlist = localStorage.getItem(WISHLIST_STORAGE_KEY);
      if (savedWishlist) {
        const wishlistItems: WishlistItem[] = JSON.parse(savedWishlist);
        dispatch({ type: 'LOAD_WISHLIST', payload: wishlistItems });
      }
    } catch (error) {
      console.error('Error loading wishlist from localStorage:', error);
    }
  }, []);

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(WISHLIST_STORAGE_KEY, JSON.stringify(state.items));
    } catch (error) {
      console.error('Error saving wishlist to localStorage:', error);
    }
  }, [state.items]);

  const addItem = (watch: SimpleWatch) => {
    dispatch({ type: 'ADD_ITEM', payload: watch });
  };

  const removeItem = (watchId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: watchId });
  };

  const isInWishlist = (watchId: string) => {
    return state.items.some(item => item.watchId === watchId);
  };

  const clearWishlist = () => {
    dispatch({ type: 'CLEAR_WISHLIST' });
  };

  const contextValue: WishlistContextType = {
    ...state,
    addItem,
    removeItem,
    isInWishlist,
    clearWishlist,
  };

  return (
    <WishlistContext.Provider value={contextValue}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist() {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
}
