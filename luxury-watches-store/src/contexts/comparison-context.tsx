'use client';

import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { SimpleWatch } from '@/lib/types';

interface ComparisonState {
  items: SimpleWatch[];
  isOpen: boolean;
}

type ComparisonAction =
  | { type: 'ADD_ITEM'; payload: SimpleWatch }
  | { type: 'REMOVE_ITEM'; payload: { id: string } }
  | { type: 'CLEAR_COMPARISON' }
  | { type: 'TOGGLE_COMPARISON' }
  | { type: 'OPEN_COMPARISON' }
  | { type: 'CLOSE_COMPARISON' }
  | { type: 'LOAD_COMPARISON'; payload: SimpleWatch[] };

interface ComparisonContextType extends ComparisonState {
  addItem: (watch: SimpleWatch) => boolean;
  removeItem: (id: string) => void;
  clearComparison: () => void;
  toggleComparison: () => void;
  openComparison: () => void;
  closeComparison: () => void;
  isInComparison: (id: string) => boolean;
  canAddMore: boolean;
  itemCount: number;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

const COMPARISON_STORAGE_KEY = 'luxury-watches-comparison';
const MAX_COMPARISON_ITEMS = 4;

function comparisonReducer(state: ComparisonState, action: ComparisonAction): ComparisonState {
  switch (action.type) {
    case 'ADD_ITEM': {
      // Don't add if already exists or if we've reached the limit
      if (state.items.some(item => item.id === action.payload.id) || 
          state.items.length >= MAX_COMPARISON_ITEMS) {
        return state;
      }
      
      const newItems = [...state.items, action.payload];
      return {
        ...state,
        items: newItems,
      };
    }

    case 'REMOVE_ITEM': {
      const newItems = state.items.filter(item => item.id !== action.payload.id);
      return {
        ...state,
        items: newItems,
      };
    }

    case 'CLEAR_COMPARISON': {
      return {
        ...state,
        items: [],
      };
    }

    case 'TOGGLE_COMPARISON': {
      return {
        ...state,
        isOpen: !state.isOpen,
      };
    }

    case 'OPEN_COMPARISON': {
      return {
        ...state,
        isOpen: true,
      };
    }

    case 'CLOSE_COMPARISON': {
      return {
        ...state,
        isOpen: false,
      };
    }

    case 'LOAD_COMPARISON': {
      return {
        ...state,
        items: action.payload,
      };
    }

    default:
      return state;
  }
}

const initialState: ComparisonState = {
  items: [],
  isOpen: false,
};

interface ComparisonProviderProps {
  children: ReactNode;
}

export function ComparisonProvider({ children }: ComparisonProviderProps) {
  const [state, dispatch] = useReducer(comparisonReducer, initialState);

  // Load comparison from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedComparison = localStorage.getItem(COMPARISON_STORAGE_KEY);
      if (savedComparison) {
        try {
          const parsedComparison = JSON.parse(savedComparison);
          dispatch({ type: 'LOAD_COMPARISON', payload: parsedComparison });
        } catch (error) {
          console.error('Failed to load comparison from localStorage:', error);
        }
      }
    }
  }, []);

  // Save comparison to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(COMPARISON_STORAGE_KEY, JSON.stringify(state.items));
    }
  }, [state.items]);

  const addItem = (watch: SimpleWatch): boolean => {
    if (state.items.some(item => item.id === watch.id)) {
      return false; // Already in comparison
    }
    
    if (state.items.length >= MAX_COMPARISON_ITEMS) {
      return false; // Comparison is full
    }

    dispatch({ type: 'ADD_ITEM', payload: watch });
    return true;
  };

  const removeItem = (id: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { id } });
  };

  const clearComparison = () => {
    dispatch({ type: 'CLEAR_COMPARISON' });
  };

  const toggleComparison = () => {
    dispatch({ type: 'TOGGLE_COMPARISON' });
  };

  const openComparison = () => {
    dispatch({ type: 'OPEN_COMPARISON' });
  };

  const closeComparison = () => {
    dispatch({ type: 'CLOSE_COMPARISON' });
  };

  const isInComparison = (id: string): boolean => {
    return state.items.some(item => item.id === id);
  };

  const contextValue: ComparisonContextType = {
    ...state,
    addItem,
    removeItem,
    clearComparison,
    toggleComparison,
    openComparison,
    closeComparison,
    isInComparison,
    canAddMore: state.items.length < MAX_COMPARISON_ITEMS,
    itemCount: state.items.length,
  };

  return (
    <ComparisonContext.Provider value={contextValue}>
      {children}
    </ComparisonContext.Provider>
  );
}

export function useComparison() {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
}

// Export constants for use in other components
export { MAX_COMPARISON_ITEMS };
