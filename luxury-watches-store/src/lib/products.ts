import { Product } from './types';

export const FEATURED_PRODUCTS: Product[] = [];

export const getAllProducts = async (): Promise<Product[]> => {
  try {
    const { getAllProducts: getSanityProducts } = await import('@/lib/sanity/utils');
    const sanityProducts = await getSanityProducts();
    return sanityProducts.map(transformSimpleWatchToProduct);
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
};

export const getProductById = (id: string): Product | undefined => {
  // For now, use static products - we'll enhance this later
  return FEATURED_PRODUCTS.find(product => product.id === id);
};

export const getProductByIdAsync = async (id: string): Promise<Product | undefined> => {
  // First try to get from Sanity
  try {
    const { getAllProducts } = await import('@/lib/sanity/utils');
    const allProducts = await getAllProducts();
    const product = allProducts.find(p => p.id === id);
    if (product) {
      return transformSimpleWatchToProduct(product);
    }
  } catch (error) {
    console.error('Error fetching from Sanity:', error);
  }

  // Fallback to static products
  return FEATURED_PRODUCTS.find(product => product.id === id);
};

// Helper function to transform SimpleWatch to Product
function transformSimpleWatchToProduct(watch: any): Product {
  return {
    id: watch.id,
    name: watch.name,
    brand: watch.brand,
    model: watch.name, // Use name as model if not available
    price: watch.price,
    originalPrice: watch.originalPrice,
    images: watch.images,
    category: watch.category,
    description: watch.description || `Luxury ${watch.brand} ${watch.name}`,
    specifications: {
      'Brand': watch.brand,
      'Model': watch.name,
      'Category': watch.category,
      'Price': `$${watch.price.toLocaleString()}`
    },
    features: watch.features || [],
    inStock: watch.inStock,
    rating: 4.8,
    reviewCount: 127,
    isNew: watch.isNew || false,
    isBestseller: watch.isBestseller || false,
    tags: [watch.category, watch.brand.toLowerCase()]
  };
}

export const getProductsByBrand = (brand: string): Product[] => {
  return FEATURED_PRODUCTS.filter(product => 
    product.brand.toLowerCase() === brand.toLowerCase()
  );
};

export const getProductsByCategory = (category: string): Product[] => {
  return FEATURED_PRODUCTS.filter(product => product.category === category);
};

export const getProductsByCollection = (collection: string): Product[] => {
  return FEATURED_PRODUCTS.filter(product => product.collection === collection);
};

export const getFeaturedProducts = (limit?: number): Product[] => {
  const featured = FEATURED_PRODUCTS.filter(product => product.isBestseller);
  return limit ? featured.slice(0, limit) : featured;
};

export const getNewProducts = (limit?: number): Product[] => {
  const newProducts = FEATURED_PRODUCTS.filter(product => product.isNew);
  return limit ? newProducts.slice(0, limit) : newProducts;
};
