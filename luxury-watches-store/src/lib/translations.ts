// Translation constants for the luxury watches store

export const TRANSLATIONS = {
  // Navigation
  nav: {
    home: 'Home',
    catalog: 'Catalog',
    brands: 'Brands',
    collections: 'Collections',
    about: 'About',
    contact: 'Contact',
    viewAllBrands: 'View All Brands',
  },

  // Common actions
  actions: {
    addToCart: 'Add to Cart',
    buyNow: 'Buy Now',
    viewDetails: 'View Details',
    learnMore: 'Learn More',
    continueShopping: 'Continue Shopping',
    proceedToCheckout: 'Proceed to Checkout',
    clearCart: 'Clear Cart',
    clearFilters: 'Clear Filters',
    applyFilters: 'Apply Filters',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    viewCart: 'View Cart',
    checkout: 'Checkout',
    remove: 'Remove',
    update: 'Update',
    save: 'Save',
    cancel: 'Cancel',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    apply: 'Apply',
  },

  // Product related
  product: {
    inStock: 'In Stock',
    outOfStock: 'Out of Stock',
    preOrder: 'Pre-Order',
    newArrival: 'New',
    bestseller: 'Bestseller',
    sale: 'Sale',
    priceOnRequest: 'Price on Request',
    features: 'Features',
    specifications: 'Specifications',
    description: 'Description',
    reviews: 'Reviews',
    relatedProducts: 'You Might Also Like',
    keyFeatures: 'Key Features',
    writeReview: 'Write a Review',
    basedOnReviews: 'Based on {count} reviews',
    customerReviews: 'Customer Reviews',
  },

  // Cart
  cart: {
    shoppingCart: 'Shopping Cart',
    cartEmpty: 'Your cart is empty',
    cartEmptyDescription: 'Add some luxury timepieces to get started',
    itemsInCart: '{count} {count, plural, one {item} other {items}} in your cart',
    subtotal: 'Subtotal',
    shipping: 'Shipping',
    tax: 'Tax',
    discount: 'Discount',
    total: 'Total',
    freeShipping: 'Free shipping included',
    freeShippingThreshold: 'Add {amount} more for free shipping',
    promoCode: 'Promo code',
    orderSummary: 'Order Summary',
    whyShopWithUs: 'Why Shop With Us',
    freeShippingOver: 'Free shipping over $1,000',
    yearWarranty: '5-year warranty',
    dayReturns: '30-day returns',
    authentic: 'Authentic',
  },

  // Catalog
  catalog: {
    watchCatalog: 'Watch Catalog',
    catalogDescription: 'Discover our exclusive collection of luxury watches from leading world brands',
    foundWatches: 'Found {count} watches',
    noResults: 'No watches found for your search',
    noResultsDescription: 'Try adjusting your filters or search terms',
    filters: 'Filters',
    brands: 'Brands',
    categories: 'Categories',
    priceRange: 'Price Range',
    searchPlaceholder: 'Search luxury watches...',
    sortBy: 'Sort by',
    viewMode: 'View Mode',
    gridView: 'Grid View',
    listView: 'List View',
  },

  // Homepage
  home: {
    heroTitle: 'Luxury Watches',
    heroSubtitle: 'Discover the finest luxury watches from prestigious world brands',
    viewCollection: 'View Collection',
    ourStory: 'Our Story',
    developmentStages: 'Development Stages',
    developmentDescription: 'Follow the progress of creating a luxury watch online store',
    projectFoundation: 'Project Foundation',
    productCatalog: 'Product Catalog',
    productPages: 'Product Pages',
    cartCheckout: 'Cart & Checkout',
    checkoutProcess: 'Checkout Process',
    userManagement: 'User Management',
    prestigiousBrands: 'Prestigious Brands',
    brandsDescription: 'We work only with the most prestigious watch brands in the world',
    viewAllBrands: 'View All Brands',
    founded: 'Founded in',
  },

  // Product page
  productPage: {
    sku: 'SKU',
    priceIncludesVat: 'Price includes VAT. Free shipping on orders over $1,000.',
    addToWishlist: 'Add to Wishlist',
    shareProduct: 'Share Product',
    featuresAndBenefits: 'Features & Benefits',
    exceptionalTimepiece: 'Exceptional timepiece',
    perfectDivingCompanion: 'Perfect diving companion',
    reviewContent1: 'This Submariner exceeded all my expectations. The build quality is outstanding, and the movement is incredibly precise. Worth every penny.',
    reviewContent2: 'Used this watch on multiple diving trips. The water resistance is excellent, and the bezel action is smooth and precise.',
  },

  // Footer
  footer: {
    aboutUs: 'About Us',
    customerService: 'Customer Service',
    myAccount: 'My Account',
    followUs: 'Follow Us',
    newsletter: 'Newsletter',
    newsletterDescription: 'Subscribe to receive updates on new arrivals and exclusive offers',
    emailPlaceholder: 'Enter your email',
    subscribe: 'Subscribe',
    allRightsReserved: 'All rights reserved.',
    privacyPolicy: 'Privacy Policy',
    termsOfService: 'Terms of Service',
    shippingInfo: 'Shipping Info',
    returns: 'Returns',
    sizeGuide: 'Size Guide',
    contactUs: 'Contact Us',
    faq: 'FAQ',
    orderTracking: 'Order Tracking',
    account: 'Account',
    orders: 'Orders',
    wishlist: 'Wishlist',
    addresses: 'Addresses',
  },

  // Status messages
  status: {
    loading: 'Loading...',
    error: 'An error occurred',
    success: 'Success!',
    addedToCart: 'Added to cart',
    removedFromCart: 'Removed from cart',
    updatedCart: 'Cart updated',
    addedToWishlist: 'Added to wishlist',
    removedFromWishlist: 'Removed from wishlist',
  },

  // Form labels
  form: {
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email',
    phone: 'Phone',
    address: 'Address',
    city: 'City',
    state: 'State',
    zipCode: 'ZIP Code',
    country: 'Country',
    company: 'Company',
    required: 'Required',
    optional: 'Optional',
  },

  // Brands
  brandSlogans: {
    rolex: 'A crown for every achievement',
    patekPhilippe: 'You never actually own a Patek Philippe',
    audemarsPiguet: 'To break the rules, you must first master them',
    omega: 'A legacy of precision',
  },

  // Categories
  categories: {
    dress: 'Dress Watches',
    sports: 'Sports Watches',
    diving: 'Diving Watches',
    pilot: 'Pilot Watches',
    chronograph: 'Chronographs',
    gmt: 'GMT Watches',
  },

  // Sort options
  sortOptions: {
    featured: 'Featured',
    priceAsc: 'Price: Low to High',
    priceDesc: 'Price: High to Low',
    brandAsc: 'Brand: A to Z',
    brandDesc: 'Brand: Z to A',
    newest: 'Newest First',
    rating: 'Highest Rated',
  },

  // Price ranges
  priceRanges: {
    under5k: 'Under $5,000',
    '5k-10k': '$5,000 - $10,000',
    '10k-25k': '$10,000 - $25,000',
    '25k-50k': '$25,000 - $50,000',
    '50k-100k': '$50,000 - $100,000',
    over100k: 'Over $100,000',
  },
};

// Helper function to get nested translation
export function t(key: string, params?: Record<string, any>): string {
  const keys = key.split('.');
  let value: any = TRANSLATIONS;
  
  for (const k of keys) {
    value = value?.[k];
  }
  
  if (typeof value !== 'string') {
    console.warn(`Translation key "${key}" not found`);
    return key;
  }
  
  // Simple parameter replacement
  if (params) {
    return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
      return params[paramKey] || match;
    });
  }
  
  return value;
}
