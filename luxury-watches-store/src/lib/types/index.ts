// Product Types
export interface Watch {
  id: string;
  name: string;
  brand: string;
  model: string;
  price: number;
  priceOnRequest?: boolean;
  description: string;
  shortDescription: string;
  images: WatchImage[];
  specifications: WatchSpecifications;
  availability: 'in-stock' | 'pre-order' | 'out-of-stock';
  category: WatchCategory;
  tags: string[];
  slug: string;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WatchImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
}

export interface WatchSpecifications {
  movement: string;
  caseSize: string;
  caseMaterial: string;
  dialColor: string;
  strapMaterial: string;
  waterResistance: string;
  complications: string[];
  powerReserve?: string;
  crystalType: string;
  weight?: string;
}

export interface WatchCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  parentId?: string;
}

// Brand Types
export interface Brand {
  id: string;
  name: string;
  slug: string;
  description: string;
  logo: string;
  founded: number;
  country: string;
  heritage: string;
  featured: boolean;
}

// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  addresses: Address[];
  wishlist: string[];
  orders: Order[];
  createdAt: string;
}

export interface Address {
  id: string;
  type: 'billing' | 'shipping';
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingAddress: Address;
  billingAddress: Address;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  watchId: string;
  watch: Watch;
  quantity: number;
  price: number;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partially-refunded';

// Cart Types
export interface CartItem {
  watchId: string;
  watch: Watch;
  quantity: number;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
}

// Filter Types
export interface WatchFilters {
  brands?: string[];
  categories?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  caseMaterials?: string[];
  movements?: string[];
  complications?: string[];
  availability?: string[];
  sortBy?: 'price-asc' | 'price-desc' | 'name-asc' | 'name-desc' | 'newest' | 'featured';
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Blog Types
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  author: {
    name: string;
    avatar: string;
  };
  tags: string[];
  publishedAt: string;
  readTime: number;
}

// Newsletter Types
export interface NewsletterSubscription {
  email: string;
  preferences: {
    newArrivals: boolean;
    exclusiveOffers: boolean;
    watchNews: boolean;
  };
}
