// Cache utilities for improved performance

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>()
  private maxSize: number

  constructor(maxSize = 100) {
    this.maxSize = maxSize
  }

  set<T>(key: string, data: T, ttlSeconds = 300): void {
    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    })
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }

  // Clean up expired items
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// Global cache instance
export const memoryCache = new MemoryCache(200)

// Browser storage cache utilities
export const browserCache = {
  set: (key: string, data: any, ttlSeconds = 300) => {
    if (typeof window === 'undefined') return

    const item = {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    }

    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to set browser cache:', error)
    }
  },

  get: <T>(key: string): T | null => {
    if (typeof window === 'undefined') return null

    try {
      const itemStr = localStorage.getItem(`cache_${key}`)
      if (!itemStr) return null

      const item = JSON.parse(itemStr)
      
      // Check if expired
      if (Date.now() - item.timestamp > item.ttl) {
        localStorage.removeItem(`cache_${key}`)
        return null
      }

      return item.data
    } catch (error) {
      console.warn('Failed to get browser cache:', error)
      return null
    }
  },

  delete: (key: string) => {
    if (typeof window === 'undefined') return
    localStorage.removeItem(`cache_${key}`)
  },

  clear: () => {
    if (typeof window === 'undefined') return
    
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith('cache_')) {
        localStorage.removeItem(key)
      }
    })
  }
}

// Cache wrapper for API calls
export function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds = 300,
  useMemory = true
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    try {
      // Try memory cache first
      if (useMemory) {
        const cached = memoryCache.get<T>(key)
        if (cached) {
          resolve(cached)
          return
        }
      }

      // Try browser cache
      const browserCached = browserCache.get<T>(key)
      if (browserCached) {
        // Also store in memory cache for faster access
        if (useMemory) {
          memoryCache.set(key, browserCached, ttlSeconds)
        }
        resolve(browserCached)
        return
      }

      // Fetch fresh data
      const data = await fetcher()
      
      // Store in both caches
      if (useMemory) {
        memoryCache.set(key, data, ttlSeconds)
      }
      browserCache.set(key, data, ttlSeconds)
      
      resolve(data)
    } catch (error) {
      reject(error)
    }
  })
}

// Cache invalidation utilities
export const cacheInvalidation = {
  // Invalidate all product-related cache
  invalidateProducts: () => {
    const patterns = ['products_', 'product_', 'catalog_', 'search_']
    
    // Clear memory cache
    patterns.forEach(pattern => {
      for (const key of memoryCache['cache'].keys()) {
        if (key.includes(pattern)) {
          memoryCache.delete(key)
        }
      }
    })

    // Clear browser cache
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cache_') && patterns.some(pattern => key.includes(pattern))) {
          localStorage.removeItem(key)
        }
      })
    }
  },

  // Invalidate blog-related cache
  invalidateBlog: () => {
    const patterns = ['blog_', 'post_']
    
    patterns.forEach(pattern => {
      for (const key of memoryCache['cache'].keys()) {
        if (key.includes(pattern)) {
          memoryCache.delete(key)
        }
      }
    })

    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cache_') && patterns.some(pattern => key.includes(pattern))) {
          localStorage.removeItem(key)
        }
      })
    }
  },

  // Invalidate user-specific cache
  invalidateUser: () => {
    const patterns = ['user_', 'cart_', 'wishlist_', 'auth_']
    
    patterns.forEach(pattern => {
      for (const key of memoryCache['cache'].keys()) {
        if (key.includes(pattern)) {
          memoryCache.delete(key)
        }
      }
    })

    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cache_') && patterns.some(pattern => key.includes(pattern))) {
          localStorage.removeItem(key)
        }
      })
    }
  }
}

// Automatic cleanup interval
if (typeof window !== 'undefined') {
  setInterval(() => {
    memoryCache.cleanup()
  }, 60000) // Clean up every minute
}
