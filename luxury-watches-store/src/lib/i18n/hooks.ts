'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  LanguageCode, 
  Language, 
  SUPPORTED_LANGUAGES, 
  DEFAULT_LANGUAGE,
  getLanguageByCode,
  getCurrencyForLanguage 
} from './config'
import { 
  formatCurrency, 
  formatDate, 
  formatNumber, 
  formatRelativeTime 
} from './formatters'

// Local storage key for language preference
const LANGUAGE_STORAGE_KEY = 'atlas-luxury-language'

// Language detection and management hook
export function useLanguage() {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>(DEFAULT_LANGUAGE)
  const [isLoading, setIsLoading] = useState(true)

  // Detect browser language
  const detectBrowserLanguage = useCallback((): LanguageCode => {
    if (typeof window === 'undefined') return DEFAULT_LANGUAGE
    
    const browserLang = navigator.language.split('-')[0]
    const supportedCodes = SUPPORTED_LANGUAGES.map(lang => lang.code)
    
    return supportedCodes.includes(browserLang as LanguageCode) 
      ? browserLang as LanguageCode 
      : DEFAULT_LANGUAGE
  }, [])

  // Load language preference
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Priority: localStorage > browser > default
    const storedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY) as LanguageCode
    const browserLanguage = detectBrowserLanguage()
    
    const languageToUse = storedLanguage && 
      SUPPORTED_LANGUAGES.some(lang => lang.code === storedLanguage)
      ? storedLanguage
      : browserLanguage

    setCurrentLanguage(languageToUse)
    setIsLoading(false)
  }, [detectBrowserLanguage])

  // Change language
  const changeLanguage = useCallback((newLanguage: LanguageCode) => {
    if (!SUPPORTED_LANGUAGES.some(lang => lang.code === newLanguage)) {
      console.warn(`Unsupported language: ${newLanguage}`)
      return
    }

    setCurrentLanguage(newLanguage)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage)
      
      // Update document language
      document.documentElement.lang = newLanguage
      
      // Update document direction for RTL languages
      const language = getLanguageByCode(newLanguage)
      document.documentElement.dir = language?.rtl ? 'rtl' : 'ltr'
    }
  }, [])

  // Get current language object
  const language = getLanguageByCode(currentLanguage)

  return {
    currentLanguage,
    language,
    changeLanguage,
    isLoading,
    supportedLanguages: SUPPORTED_LANGUAGES,
    isRTL: language?.rtl || false
  }
}

// Formatting hook with current language context
export function useFormatting() {
  const { currentLanguage } = useLanguage()

  const formatPrice = useCallback((
    amount: number,
    currency?: string
  ) => formatCurrency(amount, currentLanguage, currency), [currentLanguage])

  const formatDateLocal = useCallback((
    date: Date | string,
    style: 'short' | 'medium' | 'long' | 'full' = 'medium'
  ) => formatDate(date, currentLanguage, style), [currentLanguage])

  const formatNumberLocal = useCallback((
    number: number,
    options?: Intl.NumberFormatOptions
  ) => formatNumber(number, currentLanguage, options), [currentLanguage])

  const formatRelativeTimeLocal = useCallback((
    date: Date | string
  ) => formatRelativeTime(date, currentLanguage), [currentLanguage])

  return {
    formatPrice,
    formatDate: formatDateLocal,
    formatNumber: formatNumberLocal,
    formatRelativeTime: formatRelativeTimeLocal,
    currency: getCurrencyForLanguage(currentLanguage)
  }
}

// Currency conversion hook (placeholder for future implementation)
export function useCurrency() {
  const { currentLanguage } = useLanguage()
  const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({})
  const [isLoading, setIsLoading] = useState(false)

  const baseCurrency = getCurrencyForLanguage(currentLanguage)

  // Placeholder for exchange rate fetching
  const fetchExchangeRates = useCallback(async () => {
    setIsLoading(true)
    try {
      // TODO: Implement actual exchange rate API
      // For now, return mock rates
      setExchangeRates({
        USD: 1,
        EUR: 0.85,
        GBP: 0.73,
        JPY: 110,
        CNY: 6.45,
        RUB: 75
      })
    } catch (error) {
      console.error('Failed to fetch exchange rates:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchExchangeRates()
  }, [fetchExchangeRates])

  const convertPrice = useCallback((
    amount: number,
    fromCurrency: string,
    toCurrency?: string
  ): number => {
    const targetCurrency = toCurrency || baseCurrency
    
    if (fromCurrency === targetCurrency) return amount
    
    const fromRate = exchangeRates[fromCurrency] || 1
    const toRate = exchangeRates[targetCurrency] || 1
    
    return (amount / fromRate) * toRate
  }, [exchangeRates, baseCurrency])

  return {
    baseCurrency,
    exchangeRates,
    isLoading,
    convertPrice,
    refreshRates: fetchExchangeRates
  }
}

// Language switcher hook
export function useLanguageSwitcher() {
  const { currentLanguage, changeLanguage, supportedLanguages } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)

  const toggleSwitcher = useCallback(() => {
    setIsOpen(prev => !prev)
  }, [])

  const selectLanguage = useCallback((languageCode: LanguageCode) => {
    changeLanguage(languageCode)
    setIsOpen(false)
  }, [changeLanguage])

  const closeSwitcher = useCallback(() => {
    setIsOpen(false)
  }, [])

  return {
    currentLanguage,
    supportedLanguages,
    isOpen,
    toggleSwitcher,
    selectLanguage,
    closeSwitcher
  }
}

// URL localization hook
export function useLocalizedUrl() {
  const { currentLanguage } = useLanguage()

  const getLocalizedUrl = useCallback((
    path: string,
    language?: LanguageCode
  ): string => {
    const lang = language || currentLanguage
    
    // For Atlas Luxury, main site (.com) doesn't use language prefixes
    // English is the default and uses clean URLs
    if (lang === DEFAULT_LANGUAGE) {
      return path.startsWith('/') ? path : `/${path}`
    }
    
    // Other languages could use subdomains or query parameters
    // For now, we'll use the same clean URLs but track language in localStorage
    return path.startsWith('/') ? path : `/${path}`
  }, [currentLanguage])

  const getCanonicalUrl = useCallback((path: string): string => {
    // Always return the English (default) version as canonical
    return getLocalizedUrl(path, DEFAULT_LANGUAGE)
  }, [getLocalizedUrl])

  const getAlternateUrls = useCallback((path: string): Array<{
    hreflang: string
    href: string
  }> => {
    return SUPPORTED_LANGUAGES.map(lang => ({
      hreflang: lang.code === DEFAULT_LANGUAGE ? 'x-default' : lang.code,
      href: getLocalizedUrl(path, lang.code)
    }))
  }, [getLocalizedUrl])

  return {
    getLocalizedUrl,
    getCanonicalUrl,
    getAlternateUrls
  }
}

// Content fallback hook
export function useContentFallback() {
  const { currentLanguage } = useLanguage()

  const getContentWithFallback = useCallback(<T>(
    content: Partial<Record<LanguageCode, T>>,
    fallbackChain: LanguageCode[] = ['en', 'de', 'fr']
  ): { content: T | null, language: LanguageCode | null, isFallback: boolean } => {
    // Try current language first
    if (content[currentLanguage]) {
      return {
        content: content[currentLanguage]!,
        language: currentLanguage,
        isFallback: false
      }
    }

    // Try fallback chain
    for (const fallbackLang of fallbackChain) {
      if (content[fallbackLang]) {
        return {
          content: content[fallbackLang]!,
          language: fallbackLang,
          isFallback: true
        }
      }
    }

    return {
      content: null,
      language: null,
      isFallback: false
    }
  }, [currentLanguage])

  return {
    getContentWithFallback
  }
}

// SEO localization hook
export function useSEOLocalization() {
  const { currentLanguage, language } = useLanguage()
  const { getAlternateUrls, getCanonicalUrl } = useLocalizedUrl()

  const getSEOData = useCallback((
    path: string,
    customTitle?: string,
    customDescription?: string
  ) => {
    const alternateUrls = getAlternateUrls(path)
    const canonicalUrl = getCanonicalUrl(path)

    return {
      language: currentLanguage,
      direction: language?.rtl ? 'rtl' : 'ltr',
      alternateUrls,
      canonicalUrl,
      title: customTitle,
      description: customDescription
    }
  }, [currentLanguage, language, getAlternateUrls, getCanonicalUrl])

  return {
    getSEOData
  }
}
