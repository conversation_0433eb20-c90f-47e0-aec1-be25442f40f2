/**
 * Internationalization formatters for Atlas Luxury
 * Handles currency, dates, numbers, and locale-specific formatting
 */

import { 
  LanguageCode, 
  CURRENCY_CONFIG, 
  DATE_CONFIG, 
  NUMBER_CONFIG,
  getCurrencyForLanguage,
  getLocaleForLanguage 
} from './config'

// Currency formatting
export function formatCurrency(
  amount: number,
  languageCode: LanguageCode = 'en',
  currencyOverride?: string
): string {
  const currency = currencyOverride || getCurrencyForLanguage(languageCode)
  const locale = getLocaleForLanguage(languageCode)
  const config = CURRENCY_CONFIG[currency as keyof typeof CURRENCY_CONFIG]

  if (!config) {
    // Fallback to basic USD formatting
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: config.decimals,
      maximumFractionDigits: config.decimals
    }).format(amount)
  } catch (error) {
    // Fallback formatting
    const formattedNumber = formatNumber(amount, languageCode)
    return config.position === 'before' 
      ? `${config.symbol}${formattedNumber}`
      : `${formattedNumber}${config.symbol}`
  }
}

// Price range formatting
export function formatPriceRange(
  minPrice: number,
  maxPrice: number,
  languageCode: LanguageCode = 'en',
  currency?: string
): string {
  const min = formatCurrency(minPrice, languageCode, currency)
  const max = formatCurrency(maxPrice, languageCode, currency)
  
  const separators = {
    en: ' - ',
    de: ' - ',
    fr: ' - ',
    ja: ' ～ ',
    zh: ' - ',
    ru: ' - '
  }
  
  return `${min}${separators[languageCode] || ' - '}${max}`
}

// Number formatting
export function formatNumber(
  number: number,
  languageCode: LanguageCode = 'en',
  options: Intl.NumberFormatOptions = {}
): string {
  const locale = getLocaleForLanguage(languageCode)
  
  try {
    return new Intl.NumberFormat(locale, options).format(number)
  } catch (error) {
    // Fallback to basic formatting
    return number.toLocaleString('en-US', options)
  }
}

// Date formatting
export function formatDate(
  date: Date | string,
  languageCode: LanguageCode = 'en',
  style: 'short' | 'medium' | 'long' | 'full' = 'medium'
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const locale = getLocaleForLanguage(languageCode)
  
  const styleMap = {
    short: { dateStyle: 'short' as const },
    medium: { dateStyle: 'medium' as const },
    long: { dateStyle: 'long' as const },
    full: { dateStyle: 'full' as const }
  }
  
  try {
    return new Intl.DateTimeFormat(locale, styleMap[style]).format(dateObj)
  } catch (error) {
    return dateObj.toLocaleDateString('en-US')
  }
}

// Relative time formatting (e.g., "2 days ago")
export function formatRelativeTime(
  date: Date | string,
  languageCode: LanguageCode = 'en'
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const locale = getLocaleForLanguage(languageCode)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
  
  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
    }
  } catch (error) {
    return formatDate(dateObj, languageCode, 'short')
  }
}

// Percentage formatting
export function formatPercentage(
  value: number,
  languageCode: LanguageCode = 'en',
  decimals: number = 1
): string {
  const locale = getLocaleForLanguage(languageCode)
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value / 100)
  } catch (error) {
    return `${value.toFixed(decimals)}%`
  }
}

// List formatting (e.g., "A, B, and C")
export function formatList(
  items: string[],
  languageCode: LanguageCode = 'en',
  type: 'conjunction' | 'disjunction' = 'conjunction'
): string {
  if (items.length === 0) return ''
  if (items.length === 1) return items[0]
  
  const locale = getLocaleForLanguage(languageCode)
  
  try {
    return new Intl.ListFormat(locale, { 
      style: 'long', 
      type: type 
    }).format(items)
  } catch (error) {
    // Fallback formatting
    if (items.length === 2) {
      return items.join(' and ')
    }
    return items.slice(0, -1).join(', ') + ', and ' + items[items.length - 1]
  }
}

// File size formatting
export function formatFileSize(
  bytes: number,
  languageCode: LanguageCode = 'en'
): string {
  const units = {
    en: ['B', 'KB', 'MB', 'GB', 'TB'],
    de: ['B', 'KB', 'MB', 'GB', 'TB'],
    fr: ['o', 'Ko', 'Mo', 'Go', 'To'],
    ja: ['B', 'KB', 'MB', 'GB', 'TB'],
    zh: ['B', 'KB', 'MB', 'GB', 'TB'],
    ru: ['Б', 'КБ', 'МБ', 'ГБ', 'ТБ']
  }
  
  const unitArray = units[languageCode] || units.en
  const threshold = 1024
  
  if (bytes === 0) return `0 ${unitArray[0]}`
  
  const unitIndex = Math.floor(Math.log(bytes) / Math.log(threshold))
  const size = bytes / Math.pow(threshold, unitIndex)
  
  return `${formatNumber(size, languageCode, { 
    maximumFractionDigits: 1 
  })} ${unitArray[unitIndex]}`
}

// Phone number formatting
export function formatPhoneNumber(
  phoneNumber: string,
  languageCode: LanguageCode = 'en'
): string {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '')
  
  // Format based on language/region
  const formatters = {
    en: (num: string) => {
      if (num.length === 10) {
        return `(${num.slice(0, 3)}) ${num.slice(3, 6)}-${num.slice(6)}`
      }
      if (num.length === 11 && num.startsWith('1')) {
        return `+1 (${num.slice(1, 4)}) ${num.slice(4, 7)}-${num.slice(7)}`
      }
      return phoneNumber
    },
    de: (num: string) => {
      if (num.length >= 10) {
        return `+49 ${num.slice(-10, -7)} ${num.slice(-7, -4)} ${num.slice(-4)}`
      }
      return phoneNumber
    },
    fr: (num: string) => {
      if (num.length === 10) {
        return `${num.slice(0, 2)} ${num.slice(2, 4)} ${num.slice(4, 6)} ${num.slice(6, 8)} ${num.slice(8)}`
      }
      return phoneNumber
    },
    ja: (num: string) => {
      if (num.length >= 10) {
        return `${num.slice(0, 3)}-${num.slice(3, 7)}-${num.slice(7)}`
      }
      return phoneNumber
    },
    zh: (num: string) => {
      if (num.length === 11) {
        return `${num.slice(0, 3)} ${num.slice(3, 7)} ${num.slice(7)}`
      }
      return phoneNumber
    },
    ru: (num: string) => {
      if (num.length >= 10) {
        return `+7 (${num.slice(-10, -7)}) ${num.slice(-7, -4)}-${num.slice(-4, -2)}-${num.slice(-2)}`
      }
      return phoneNumber
    }
  }
  
  const formatter = formatters[languageCode] || formatters.en
  return formatter(cleaned)
}

// Address formatting
export function formatAddress(
  address: {
    street?: string
    city?: string
    state?: string
    postalCode?: string
    country?: string
  },
  languageCode: LanguageCode = 'en'
): string {
  const { street, city, state, postalCode, country } = address
  
  // Different address formats by region
  const formatters = {
    en: () => [street, `${city}, ${state} ${postalCode}`, country].filter(Boolean).join('\n'),
    de: () => [street, `${postalCode} ${city}`, country].filter(Boolean).join('\n'),
    fr: () => [street, `${postalCode} ${city}`, country].filter(Boolean).join('\n'),
    ja: () => [country, `${state}${city}`, street].filter(Boolean).join('\n'),
    zh: () => [country, `${state}${city}`, street].filter(Boolean).join('\n'),
    ru: () => [street, `${city}, ${postalCode}`, country].filter(Boolean).join('\n')
  }
  
  const formatter = formatters[languageCode] || formatters.en
  return formatter()
}

// Text truncation with locale-aware ellipsis
export function truncateText(
  text: string,
  maxLength: number,
  languageCode: LanguageCode = 'en'
): string {
  if (text.length <= maxLength) return text
  
  const ellipsis = {
    en: '...',
    de: '...',
    fr: '...',
    ja: '…',
    zh: '…',
    ru: '...'
  }
  
  const suffix = ellipsis[languageCode] || '...'
  return text.slice(0, maxLength - suffix.length) + suffix
}
