/**
 * Internationalization configuration for Atlas Luxury
 * Supporting 6 languages targeting luxury market segments
 */

export const SUPPORTED_LANGUAGES = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    region: 'US',
    currency: 'USD',
    isDefault: true,
    rtl: false
  },
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    region: 'DE',
    currency: 'EUR',
    isDefault: false,
    rtl: false
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    region: 'FR',
    currency: 'EUR',
    isDefault: false,
    rtl: false
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    region: 'JP',
    currency: 'JPY',
    isDefault: false,
    rtl: false
  },
  {
    code: 'zh',
    name: 'Chinese Simplified',
    nativeName: '简体中文',
    flag: '🇨🇳',
    region: 'CN',
    currency: 'CNY',
    isDefault: false,
    rtl: false
  },
  {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    region: 'RU',
    currency: 'RUB',
    isDefault: false,
    rtl: false
  }
] as const

export type LanguageCode = typeof SUPPORTED_LANGUAGES[number]['code']
export type Language = typeof SUPPORTED_LANGUAGES[number]

export const DEFAULT_LANGUAGE: LanguageCode = 'en'

// Currency formatting configuration
export const CURRENCY_CONFIG = {
  USD: { symbol: '$', position: 'before', decimals: 2 },
  EUR: { symbol: '€', position: 'after', decimals: 2 },
  GBP: { symbol: '£', position: 'before', decimals: 2 },
  JPY: { symbol: '¥', position: 'before', decimals: 0 },
  CNY: { symbol: '¥', position: 'before', decimals: 2 },
  RUB: { symbol: '₽', position: 'after', decimals: 2 }
} as const

// Date formatting configuration
export const DATE_CONFIG = {
  en: { locale: 'en-US', format: 'MM/dd/yyyy' },
  de: { locale: 'de-DE', format: 'dd.MM.yyyy' },
  fr: { locale: 'fr-FR', format: 'dd/MM/yyyy' },
  ja: { locale: 'ja-JP', format: 'yyyy/MM/dd' },
  zh: { locale: 'zh-CN', format: 'yyyy/MM/dd' },
  ru: { locale: 'ru-RU', format: 'dd.MM.yyyy' }
} as const

// Number formatting configuration
export const NUMBER_CONFIG = {
  en: { locale: 'en-US', thousandsSeparator: ',', decimalSeparator: '.' },
  de: { locale: 'de-DE', thousandsSeparator: '.', decimalSeparator: ',' },
  fr: { locale: 'fr-FR', thousandsSeparator: ' ', decimalSeparator: ',' },
  ja: { locale: 'ja-JP', thousandsSeparator: ',', decimalSeparator: '.' },
  zh: { locale: 'zh-CN', thousandsSeparator: ',', decimalSeparator: '.' },
  ru: { locale: 'ru-RU', thousandsSeparator: ' ', decimalSeparator: ',' }
} as const

// URL structure configuration
export const URL_CONFIG = {
  // Main site uses .com/ without language prefix (English default)
  useLanguagePrefix: false,
  defaultLanguageInUrl: false,
  languageDetection: {
    browser: true,
    localStorage: true,
    subdomain: false,
    cookie: true
  }
}

// Content fallback strategy
export const CONTENT_FALLBACK = {
  // If content not available in requested language, fall back to:
  fallbackChain: ['en', 'de', 'fr'] as LanguageCode[],
  showFallbackIndicator: true,
  autoTranslateMetadata: false
}

// SEO configuration per language
export const SEO_CONFIG = {
  en: {
    titleSuffix: ' | Atlas Luxury',
    defaultTitle: 'Atlas Luxury - Premium Watches & Fine Jewelry',
    defaultDescription: 'Discover the world\'s most exquisite timepieces and jewelry. Luxury watches, fine jewelry, and exclusive accessories from prestigious brands.',
    keywords: ['luxury watches', 'fine jewelry', 'premium timepieces', 'swiss watches', 'luxury accessories']
  },
  de: {
    titleSuffix: ' | Atlas Luxury',
    defaultTitle: 'Atlas Luxury - Premium Uhren & Schmuck',
    defaultDescription: 'Entdecken Sie die exquisitesten Zeitmesser und Schmuckstücke der Welt. Luxusuhren, edler Schmuck und exklusive Accessoires von prestigeträchtigen Marken.',
    keywords: ['luxusuhren', 'edler schmuck', 'premium zeitmesser', 'schweizer uhren', 'luxus accessoires']
  },
  fr: {
    titleSuffix: ' | Atlas Luxury',
    defaultTitle: 'Atlas Luxury - Montres Premium & Bijoux',
    defaultDescription: 'Découvrez les garde-temps et bijoux les plus exquis au monde. Montres de luxe, bijoux fins et accessoires exclusifs de marques prestigieuses.',
    keywords: ['montres de luxe', 'bijoux fins', 'garde-temps premium', 'montres suisses', 'accessoires de luxe']
  },
  ja: {
    titleSuffix: ' | Atlas Luxury',
    defaultTitle: 'Atlas Luxury - プレミアム時計＆ジュエリー',
    defaultDescription: '世界最高級の時計とジュエリーをご覧ください。高級時計、ファインジュエリー、名門ブランドの限定アクセサリー。',
    keywords: ['高級時計', 'ファインジュエリー', 'プレミアム時計', 'スイス時計', '高級アクセサリー']
  },
  zh: {
    titleSuffix: ' | Atlas Luxury',
    defaultTitle: 'Atlas Luxury - 高端腕表与珠宝',
    defaultDescription: '探索世界上最精美的时计和珠宝。奢华腕表、精美珠宝以及来自知名品牌的独家配饰。',
    keywords: ['奢华腕表', '精美珠宝', '高端时计', '瑞士腕表', '奢华配饰']
  },
  ru: {
    titleSuffix: ' | Atlas Luxury',
    defaultTitle: 'Atlas Luxury - Премиальные Часы и Ювелирные Изделия',
    defaultDescription: 'Откройте для себя самые изысканные часы и ювелирные изделия в мире. Роскошные часы, изящные украшения и эксклюзивные аксессуары от престижных брендов.',
    keywords: ['роскошные часы', 'изящные украшения', 'премиальные часы', 'швейцарские часы', 'роскошные аксессуары']
  }
} as const

// Market-specific configurations
export const MARKET_CONFIG = {
  en: {
    priceDisplay: 'prominent',
    showDiscount: true,
    emphasizeExclusivity: true,
    culturalNotes: 'Direct communication, value proposition focus'
  },
  de: {
    priceDisplay: 'detailed',
    showDiscount: false,
    emphasizeExclusivity: true,
    culturalNotes: 'Quality emphasis, technical specifications important'
  },
  fr: {
    priceDisplay: 'elegant',
    showDiscount: false,
    emphasizeExclusivity: true,
    culturalNotes: 'Aesthetic focus, heritage and craftsmanship emphasis'
  },
  ja: {
    priceDisplay: 'respectful',
    showDiscount: false,
    emphasizeExclusivity: true,
    culturalNotes: 'Attention to detail, service quality, brand prestige'
  },
  zh: {
    priceDisplay: 'prominent',
    showDiscount: true,
    emphasizeExclusivity: true,
    culturalNotes: 'Status symbol emphasis, investment value, authenticity'
  },
  ru: {
    priceDisplay: 'prominent',
    showDiscount: true,
    emphasizeExclusivity: true,
    culturalNotes: 'Luxury status, exclusivity, premium positioning'
  }
} as const

// Helper functions
export function getLanguageByCode(code: string): Language | undefined {
  return SUPPORTED_LANGUAGES.find(lang => lang.code === code)
}

export function getDefaultLanguage(): Language {
  return SUPPORTED_LANGUAGES.find(lang => lang.isDefault) || SUPPORTED_LANGUAGES[0]
}

export function getCurrencyForLanguage(languageCode: LanguageCode): string {
  const language = getLanguageByCode(languageCode)
  return language?.currency || 'USD'
}

export function getLocaleForLanguage(languageCode: LanguageCode): string {
  return DATE_CONFIG[languageCode]?.locale || 'en-US'
}
