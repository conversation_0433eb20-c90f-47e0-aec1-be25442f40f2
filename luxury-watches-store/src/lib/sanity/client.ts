import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'

// Client for public read operations (no token needed)
export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '81887gvx',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  useCdn: false, // Disable CDN for real-time data
  apiVersion: '2024-01-01',
})

// Client for server-side operations with token
export const serverClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '81887gvx',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  useCdn: false,
  apiVersion: '2024-01-01',
  token: process.env.SANITY_API_TOKEN,
})

// Helper for generating image URLs
const builder = imageUrlBuilder(client)

export function urlFor(source: any) {
  return builder.image(source)
}

// GROQ queries for fetching data
export const queries = {
  // Products
  allProducts: `*[_type == "product" && isAvailable == true] | order(_createdAt desc) {
    _id,
    name,
    slug,
    "brand": brand->name,
    "brandSlug": brand->slug.current,
    "category": category->name,
    "categorySlug": category->slug.current,
    "collection": collection->name,
    "collectionSlug": collection->slug.current,
    images,
    price,
    originalPrice,
    currency,
    description,
    specifications,
    stock,
    isAvailable,
    isFeatured,
    tags,
    _createdAt
  }`,

  productBySlug: `*[_type == "product" && slug.current == $slug][0] {
    _id,
    name,
    slug,
    "brand": brand->{name, slug, description, logo},
    "category": category->{name, slug},
    "collection": collection->{name, slug},
    images,
    price,
    originalPrice,
    currency,
    description,
    specifications,
    stock,
    isAvailable,
    isFeatured,
    tags,
    seo,
    _createdAt
  }`,

  productById: `*[_type == "product" && _id == $id][0] {
    _id,
    name,
    slug,
    "brand": brand->{name, slug, description, logo},
    "category": category->{name, slug},
    "collection": collection->{name, slug},
    images,
    price,
    originalPrice,
    currency,
    description,
    specifications,
    stock,
    isAvailable,
    isFeatured,
    tags,
    seo,
    _createdAt
  }`,

  featuredProducts: `*[_type == "product" && isFeatured == true && isAvailable == true] | order(_createdAt desc) [0...6] {
    _id,
    name,
    slug,
    "brand": brand->name,
    "brandSlug": brand->slug.current,
    images,
    price,
    originalPrice,
    currency,
    _createdAt
  }`,

  // Jewelry
  allJewelry: `*[_type == "jewelry" && isAvailable == true] | order(_createdAt desc) {
    _id,
    name,
    slug,
    "brand": brand->name,
    "brandSlug": brand->slug.current,
    "category": category->name,
    "categorySlug": category->slug.current,
    images,
    price,
    originalPrice,
    currency,
    description,
    specifications,
    stock,
    isAvailable,
    isFeatured,
    tags,
    _createdAt
  }`,

  featuredJewelry: `*[_type == "jewelry" && isFeatured == true && isAvailable == true] | order(_createdAt desc) [0...6] {
    _id,
    name,
    slug,
    "brand": brand->name,
    "brandSlug": brand->slug.current,
    images,
    price,
    originalPrice,
    currency,
    _createdAt
  }`,

  jewelryById: `*[_type == "jewelry" && _id == $id][0] {
    _id,
    name,
    slug,
    "brand": brand->{name, slug, description, logo},
    "category": category->{name, slug},
    images,
    price,
    originalPrice,
    currency,
    description,
    specifications,
    stock,
    isAvailable,
    isFeatured,
    tags,
    _createdAt
  }`,

  // Brands
  allBrands: `*[_type == "brand"] | order(name asc) {
    _id,
    name,
    slug,
    logo,
    description,
    isLuxury,
    priceRange
  }`,

  brandBySlug: `*[_type == "brand" && slug.current == $slug][0] {
    _id,
    name,
    slug,
    logo,
    description,
    founded,
    country,
    website,
    heritage,
    isLuxury,
    priceRange,
    seo
  }`,

  // Categories
  allCategories: `*[_type == "category" && isActive == true] | order(sortOrder asc) {
    _id,
    name,
    slug,
    description,
    icon,
    image
  }`,

  // Collections
  allCollections: `*[_type == "collection" && isActive == true] | order(_createdAt desc) {
    _id,
    name,
    slug,
    "brand": brand->name,
    "brandSlug": brand->slug.current,
    image,
    description,
    launchYear,
    isFeatured
  }`,

  // Reviews
  productReviews: `*[_type == "review" && product._ref == $productId && isApproved == true] | order(_createdAt desc) {
    _id,
    customerName,
    rating,
    title,
    content,
    images,
    isVerified,
    helpfulCount,
    createdAt
  }`,

  // Blog posts
  allBlogPosts: `*[_type == "blogPost" && isPublished == true] | order(publishedAt desc) {
    _id,
    title,
    slug,
    excerpt,
    featuredImage,
    author,
    publishedAt,
    categories,
    tags
  }`,

  blogPostBySlug: `*[_type == "blogPost" && slug.current == $slug && isPublished == true][0] {
    _id,
    title,
    slug,
    excerpt,
    featuredImage,
    content,
    author,
    publishedAt,
    categories,
    tags,
    seo
  }`,

  // Site settings
  siteSettings: `*[_type == "siteSettings"][0] {
    title,
    description,
    logo,
    favicon,
    socialMedia,
    contact,
    businessHours,
    seo,
    analytics
  }`
}
