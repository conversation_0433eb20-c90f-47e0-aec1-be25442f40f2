// Luxury Watch Brands
export const LUXURY_BRANDS = [
  {
    name: 'Rolex',
    slug: 'rolex',
    founded: 1905,
    country: 'Switzerland',
    description: 'A crown for every achievement',
  },
  {
    name: '<PERSON><PERSON>',
    slug: 'patek-philippe',
    founded: 1839,
    country: 'Switzerland',
    description: 'You never actually own a Patek <PERSON>',
  },
  {
    name: '<PERSON>demar<PERSON> Piguet',
    slug: 'audemars-piguet',
    founded: 1875,
    country: 'Switzerland',
    description: 'To break the rules, you must first master them',
  },
  {
    name: '<PERSON>acher<PERSON> Constantin',
    slug: 'vacheron-constantin',
    founded: 1755,
    country: 'Switzerland',
    description: 'One of a kind',
  },
  {
    name: '<PERSON><PERSON><PERSON>-<PERSON>lt<PERSON>',
    slug: 'jaeger-lecoultre',
    founded: 1833,
    country: 'Switzerland',
    description: 'The watchmaker of watchmakers',
  },
  {
    name: 'Omega',
    slug: 'omega',
    founded: 1848,
    country: 'Switzerland',
    description: 'A legacy of precision',
  },
  {
    name: '<PERSON><PERSON>',
    slug: 'cartier',
    founded: 1847,
    country: 'France',
    description: 'The jeweler of kings',
  },
  {
    name: '<PERSON><PERSON>',
    slug: 'iwc',
    founded: 1868,
    country: 'Switzerland',
    description: 'Engineered for men',
  },
] as const;

// Watch Categories
export const WATCH_CATEGORIES = [
  {
    name: 'Dress Watches',
    slug: 'dress-watches',
    description: 'Elegant timepieces for formal occasions',
  },
  {
    name: 'Sports Watches',
    slug: 'sports-watches',
    description: 'Robust watches for active lifestyles',
  },
  {
    name: 'Diving Watches',
    slug: 'diving-watches',
    description: 'Water-resistant watches for underwater adventures',
  },
  {
    name: 'Pilot Watches',
    slug: 'pilot-watches',
    description: 'Aviation-inspired timepieces',
  },
  {
    name: 'Chronographs',
    slug: 'chronographs',
    description: 'Watches with stopwatch functionality',
  },
  {
    name: 'GMT Watches',
    slug: 'gmt-watches',
    description: 'Multi-timezone timepieces for travelers',
  },
  {
    name: 'Complications',
    slug: 'complications',
    description: 'Watches with advanced mechanical features',
  },
] as const;

// Case Materials
export const CASE_MATERIALS = [
  'Stainless Steel',
  'Gold',
  'Rose Gold',
  'White Gold',
  'Platinum',
  'Titanium',
  'Ceramic',
  'Carbon Fiber',
  'Bronze',
] as const;

// Movement Types
export const MOVEMENT_TYPES = [
  'Automatic',
  'Manual',
  'Quartz',
  'Solar',
  'Kinetic',
] as const;

// Complications
export const COMPLICATIONS = [
  'Date',
  'Day-Date',
  'GMT',
  'Chronograph',
  'Moon Phase',
  'Power Reserve',
  'Annual Calendar',
  'Perpetual Calendar',
  'Minute Repeater',
  'Tourbillon',
  'World Time',
] as const;

// Price Ranges
export const PRICE_RANGES = [
  { label: 'Under $5,000', min: 0, max: 5000 },
  { label: '$5,000 - $10,000', min: 5000, max: 10000 },
  { label: '$10,000 - $25,000', min: 10000, max: 25000 },
  { label: '$25,000 - $50,000', min: 25000, max: 50000 },
  { label: '$50,000 - $100,000', min: 50000, max: 100000 },
  { label: 'Over $100,000', min: 100000, max: Infinity },
] as const;

// Sort Options
export const SORT_OPTIONS = [
  { label: 'Featured', value: 'featured' },
  { label: 'Newest', value: 'newest' },
  { label: 'Price: Low to High', value: 'price-asc' },
  { label: 'Price: High to Low', value: 'price-desc' },
  { label: 'Name: A to Z', value: 'name-asc' },
  { label: 'Name: Z to A', value: 'name-desc' },
] as const;

// Site Configuration
export const SITE_CONFIG = {
  name: 'Luxury Timepieces',
  description: 'Discover the world\'s finest luxury watches from prestigious brands',
  url: 'https://luxury-timepieces.com',
  ogImage: '/og-image.jpg',
  links: {
    twitter: 'https://twitter.com/luxurytimepieces',
    instagram: 'https://instagram.com/luxurytimepieces',
    facebook: 'https://facebook.com/luxurytimepieces',
  },
  contact: {
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Fifth Avenue, New York, NY 10001',
  },
} as const;

// Navigation Links
export const NAVIGATION_LINKS = [
  { name: 'Home', href: '/' },
  { name: 'Watches', href: '/catalog' },
  { name: 'Brands', href: '/brands' },
  { name: 'About', href: '/about' },
  { name: 'Journal', href: '/blog' },
  { name: 'Contact', href: '/contact' },
] as const;

// Footer Links
export const FOOTER_LINKS = {
  shop: [
    { name: 'All Watches', href: '/catalog' },
    { name: 'New Arrivals', href: '/catalog?sort=newest' },
    { name: 'Featured', href: '/catalog?sort=featured' },
    { name: 'Sale', href: '/sale' },
  ],
  brands: LUXURY_BRANDS.slice(0, 6).map(brand => ({
    name: brand.name,
    href: `/brands/${brand.slug}`,
  })),
  support: [
    { name: 'Size Guide', href: '/size-guide' },
    { name: 'Care Instructions', href: '/care' },
    { name: 'Warranty', href: '/warranty' },
    { name: 'Returns', href: '/returns' },
    { name: 'Shipping', href: '/shipping' },
    { name: 'FAQ', href: '/faq' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press', href: '/press' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
  ],
} as const;

// Currency Configuration
export const CURRENCY_CONFIG = {
  default: 'USD',
  symbol: '$',
  locale: 'en-US',
} as const;

// Shipping Configuration
export const SHIPPING_CONFIG = {
  freeShippingThreshold: 1000,
  standardShipping: 25,
  expressShipping: 50,
  overnightShipping: 100,
} as const;
