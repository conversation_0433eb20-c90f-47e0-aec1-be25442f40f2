import { defineConfig } from 'sanity'
import { structureTool } from 'sanity/structure'
import { visionTool } from '@sanity/vision'
import { schemaTypes } from './sanity/schemas'
import { structure } from './sanity/structure'
import { apiVersion, dataset, projectId } from './sanity/env'
import { ProductPreview } from './sanity/components/product-preview'
import { MediaLibrary } from './sanity/components/media-library'
import { AnalyticsDashboard } from './sanity/components/analytics-dashboard'

export default defineConfig({
  name: 'atlas-luxury',
  title: 'Atlas Luxury CMS',

  projectId,
  dataset,

  basePath: '/studio',

  plugins: [
    structureTool({
      structure,
      defaultDocumentNode: (S, { schemaType }) => {
        // Customize document views for specific schema types
        if (schemaType === 'product') {
          return S.document().views([
            S.view.form(),
            S.view.component(ProductPreview).title('Preview').icon(() => '👁️')
          ])
        }

        if (schemaType === 'blogPost') {
          return S.document().views([
            S.view.form(),
            S.view.component(() => null).title('Web Preview').icon(() => '🌐')
          ])
        }

        if (schemaType === 'landingPage') {
          return S.document().views([
            S.view.form(),
            S.view.component(() => null).title('Page Preview').icon(() => '📄')
          ])
        }

        return S.document()
      }
    }),
    visionTool({
      defaultApiVersion: apiVersion,
      defaultDataset: dataset
    })
  ],

  schema: {
    types: schemaTypes,
  },

  studio: {
    components: {
      // Custom studio components
      navbar: (props) => {
        return (
          <div style={{
            background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
            color: '#d4af37',
            padding: '12px 20px',
            borderBottom: '2px solid #d4af37',
            boxShadow: '0 2px 10px rgba(212, 175, 55, 0.1)'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '16px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span style={{ fontSize: '24px' }}>⚜️</span>
                  <span style={{
                    fontSize: '20px',
                    fontWeight: 'bold',
                    background: 'linear-gradient(45deg, #d4af37, #f4d03f)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}>
                    Atlas Luxury CMS
                  </span>
                </div>
                <div style={{
                  display: 'flex',
                  gap: '8px'
                }}>
                  <span style={{
                    fontSize: '11px',
                    background: '#d4af37',
                    color: '#1a1a1a',
                    padding: '3px 8px',
                    borderRadius: '12px',
                    fontWeight: '600'
                  }}>
                    PREMIUM
                  </span>
                  <span style={{
                    fontSize: '11px',
                    background: 'rgba(212, 175, 55, 0.2)',
                    color: '#d4af37',
                    padding: '3px 8px',
                    borderRadius: '12px',
                    border: '1px solid rgba(212, 175, 55, 0.3)'
                  }}>
                    v2.0
                  </span>
                </div>
              </div>
              <div style={{
                fontSize: '12px',
                opacity: 0.8,
                fontStyle: 'italic'
              }}>
                Luxury E-commerce Content Management
              </div>
            </div>
          </div>
        )
      },

      // Custom layout component
      layout: (props) => {
        return (
          <div style={{
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
          }}>
            {props.renderDefault(props)}
          </div>
        )
      }
    }
  },

  // Enable real-time updates
  useCdn: false,

  // API version
  apiVersion,

  // Document actions
  document: {
    actions: (prev, context) => {
      // Customize document actions based on schema type
      if (context.schemaType === 'siteSettings') {
        // Remove duplicate and delete actions for singleton documents
        return prev.filter(({ action }) => !['duplicate', 'delete'].includes(action || ''))
      }
      return prev
    }
  }
})
