import { defineConfig } from 'sanity'
import { structureTool } from 'sanity/structure'
import { visionTool } from '@sanity/vision'
import { schemaTypes } from './sanity/schemas'
import { structure } from './sanity/structure'
import { apiVersion, dataset, projectId } from './sanity/env'

export default defineConfig({
  name: 'atlas-luxury',
  title: 'Atlas Luxury CMS',

  projectId,
  dataset,

  basePath: '/studio',

  plugins: [
    structureTool({
      structure,
      defaultDocumentNode: (S, { schemaType }) => {
        // Customize document views for specific schema types
        if (schemaType === 'product') {
          return S.document().views([
            S.view.form(),
            S.view.component(() => null).title('Preview') // Placeholder for custom preview
          ])
        }
        return S.document()
      }
    }),
    visionTool({
      defaultApiVersion: apiVersion,
      defaultDataset: dataset
    })
  ],

  schema: {
    types: schemaTypes,
  },

  studio: {
    components: {
      // Custom studio components can be added here
    }
  },

  // Enable real-time updates
  useCdn: false,

  // API version
  apiVersion,

  // Document actions
  document: {
    actions: (prev, context) => {
      // Customize document actions based on schema type
      if (context.schemaType === 'siteSettings') {
        // Remove duplicate and delete actions for singleton documents
        return prev.filter(({ action }) => !['duplicate', 'delete'].includes(action || ''))
      }
      return prev
    }
  }
})
